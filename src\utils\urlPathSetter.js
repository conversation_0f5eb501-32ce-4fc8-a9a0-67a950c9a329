const urlPathSetter = (values) => {
  if (!values) {
    return history.pushState(null, null, `/choosePath${window.location.search}`)
  }
  if (values.path === 'hea') {
    return history.pushState(
      null,
      null,
      `/HEA/${values.step}${window.location.search}`
    )
  }
  if (values.path === 'hvac') {
    if (!values.salesOrService) {
      return history.pushState(
        null,
        null,
        `/HVAC/${values.step}${window.location.search}`
      )
    }
    if (values.salesOrService === 'service') {
      return history.pushState(
        null,
        null,
        `/HVACService/${values.step}${window.location.search}`
      )
    }
    if (values.salesOrService === 'sales') {
      return history.pushState(
        null,
        null,
        `/HVACSales/${values.step}${window.location.search}`
      )
    }
  }
}

export default urlPathSetter
