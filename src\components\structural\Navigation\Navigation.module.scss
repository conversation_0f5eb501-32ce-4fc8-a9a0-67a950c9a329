.navigation {
  background-color: $c-white;
  border-color: $c-white;
  display: flex;
  flex: 0 0 $s-base-8;
  padding: 2px $s-base-2;
}

/**
  ===========================
  Logo
  ===========================
**/

.logo {
  border: 0;
  padding: $s-base-1 0;
  @include breakpoint(below, $tablet) {
    width: 100%;
  }
  @include breakpoint(above, $tablet) {
    flex-shrink: 0;
  }

  svg {
    display: block;
    height: $s-base-6;
    margin: 0 auto;
    width: auto;
  }
}

/**
  ===========================
  Links
  ===========================
**/

.wrapper {
  display: flex;
  flex-grow: 1;
  overflow: hidden;
  padding-left: $s-base-3;
  @include breakpoint(below, $tablet) {
    display: none;
  }
}

@mixin circle($color) {
  background-image: radial-gradient(
    circle at 0px $s-base-4,
    rgba(0, 0, 0, 0) 0,
    rgba(0, 0, 0, 0) (2 + $s-base-4),
    $color (3 + $s-base-4)
  );
}

.item {
  @include t-scale-0(false);
  align-items: center;
  border-radius: 0 100px 100px 0;
  display: flex;
  flex-grow: 1;
  flex-shrink: 1;
  font-weight: 600;
  justify-content: center;
  margin-bottom: 0;
  margin-left: (0 - $s-base-4 + 2);
  overflow: hidden;
  padding: 0 $s-base-2 0 $s-base-5;
  position: relative;
  text-align: center;
  user-select: none;

  &.disabled {
    @include circle($c-light-5);
    color: $c-type-body;
    cursor: default;
  }

  &.active {
    @include circle($c-blue);
    color: $c-white;
    cursor: default;
  }

  &.completed {
    @include circle($c-green);
    color: $c-white;
    cursor: pointer;

    &:hover,
    &:focus {
      @include circle($c-green-light-25);
    }
  }

  &.completed.disabled {
    @include circle($c-green);
    color: $c-white;
    cursor: default;

    &:hover,
    &:focus {
      @include circle($c-green);
    }
  }
}

// Fixes overlapping issue
.item::after {
  bottom: 0;
  content: '';
  display: block;
  height: 100%;
  position: absolute;
  right: 0;
  top: 0;
  width: $s-base-4;
  z-index: 2;
}
