const getVeaAppointmentWindow = (time) => {
  time = time.format('LT')
  const appointmentWindows = {
    '9:00 AM': '9:00 AM - 10:00 AM',
    '10:30 AM': '10:30 AM - 11:30 AM',
    '12:00 PM': '12:00 PM - 1:00 PM',
    '1:30 PM': '1:30 PM - 2:30 PM',
    '3:30 PM': '3:30 PM - 4:30 PM',
    '5:30 PM': '5:30 PM - 6:30 PM',
  }
  if (appointmentWindows[time]) return appointmentWindows[time]
  return time
}

export default getVeaAppointmentWindow
