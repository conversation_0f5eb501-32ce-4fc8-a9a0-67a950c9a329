import { connect as FormikConnect } from 'formik'
import { useEffect } from 'react'
import PropTypes from 'prop-types'
import usePrev from '@utils/usePrev'

const FormikEffect = ({ onChange, formik }) => {
  const { values } = formik
  const prevValues = usePrev(values)

  useEffect(() => {
    // Do not run effect on form init and only when an onChange prop is passed
    if (prevValues && onChange) {
      onChange({ prevValues, nextValues: values, formik })
    }
  }, [values])

  return null
}

FormikEffect.propTypes = {
  onChange: PropTypes.func.isRequired,
  formik: PropTypes.object,
}

export default FormikConnect(FormikEffect)
