import React, { useEffect } from 'react'
import PropTypes from 'prop-types'
import * as yup from 'yup'
import { Field } from 'formik'

import ConditionalBlock from '@components/ui/ConditionalBlock'
import { Radio, Textarea, Input } from '@components/form'

const HvacService = ({ currentStepIndex, setValidationSchema, values }) => {
  const options = {
    serviceReason: [
      {
        value: 'fix',
        label: "Experiencing an issue with my system's operation",
      },
      {
        value: 'service',
        label: 'Seasonal tune-up of my current system',
      },
    ],
    systemToFix: [
      {
        value: 'furnaceOrBoiler',
        label: 'I have an issue with my furnace or boiler',
      },
      { value: 'ac', label: 'I have an issue with my air conditioning' },
      { value: 'waterHeater', label: 'I have an issue with my water heater' },
    ],
    systemToService: [
      {
        value: 'furnaceOrBoiler',
        label: 'I would like a tune up for my furnace or boiler',
      },
      { value: 'ac', label: 'I would like a tune up for my air conditioning' },
      {
        value: 'waterHeater',
        label: 'I would like a tune up for my water heater',
      },
    ],
  }

  useEffect(() => {
    setValidationSchema(
      yup.object().shape({
        serviceReason: yup
          .string()
          .oneOf(options.serviceReason.map(({ value }) => value))
          .when('salesOrService', {
            is: 'service',
            then: yup.string().required('This field is required.'),
          }),
        systemServiceAge: yup.string().when('serviceReason', {
          is: 'service',
          then: yup
            .string()
            .trim()
            .required('This field is required.')
            .max(50, 'Must be 50 characters or less'),
        }),
        lastServiced: yup.string().when('serviceReason', {
          is: 'service',
          then: yup
            .string()
            .trim()
            .required('This field is required.')
            .max(50, 'Must be 50 characters or less'),
        }),
        systemToFix: yup
          .string()
          .oneOf(options.systemToFix.map(({ value }) => value))
          .when('serviceReason', {
            is: 'fix',
            then: yup.string().required('This field is required.'),
          }),
        fixNotes: yup.string().when('serviceReason', {
          is: 'fix',
          then: yup
            .string()
            .trim()
            .required('This field is required.')
            .max(240, 'Must be 240 characters or less'),
        }),
        systemToService: yup
          .string()
          .oneOf(options.systemToService.map(({ value }) => value))
          .when('serviceReason', {
            is: 'service',
            then: yup.string().required('This field is required.'),
          }),
      })
    )
  }, [currentStepIndex, values.system])

  return (
    <>
      <div>
        <Field
          component={Radio}
          maxColumns={2}
          label="What can we help you with?"
          name="serviceReason"
          options={options.serviceReason}
        />

        <ConditionalBlock render={values.serviceReason === 'service'}>
          <Field
            component={Radio}
            maxColumns={2}
            label="What type of equipment do you want to tune up?"
            name="systemToService"
            options={options.systemToService}
          />
        </ConditionalBlock>

        <ConditionalBlock render={values.serviceReason === 'service'}>
          <Field
            component={Input}
            label="How old is your system? (Max 50 characters)"
            name="systemServiceAge"
            type="text"
          />
          <Field
            component={Input}
            label="When was it last serviced? (Max 50 characters)"
            name="lastServiced"
            type="text"
          />
        </ConditionalBlock>
        <ConditionalBlock render={values.serviceReason === 'fix'}>
          <Field
            component={Radio}
            maxColumns={2}
            label="What type of issue are you experiencing?"
            name="systemToFix"
            options={options.systemToFix}
          />
        </ConditionalBlock>
      </div>

      <ConditionalBlock render={values.serviceReason === 'fix'}>
        <br />
        <br />
        <Field
          component={Textarea}
          maxColumns={2}
          label="Please briefly describe the issue you are experiencing, with any relevant details. (Max 240 characters)"
          name="fixNotes"
        />
      </ConditionalBlock>
    </>
  )
}

HvacService.propTypes = {
  currentStepIndex: PropTypes.number.isRequired,
  setValidationSchema: PropTypes.func.isRequired,
  values: PropTypes.object.isRequired,
}

export default HvacService
