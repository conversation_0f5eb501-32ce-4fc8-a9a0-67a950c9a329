{"parser": "babel-es<PERSON>", "parserOptions": {"ecmaVersion": 2018, "sourceType": "module", "ecmaFeatures": {"jsx": true}}, "extends": ["eslint:recommended", "plugin:react/recommended", "plugin:prettier/recommended", "prettier/react"], "plugins": ["react"], "env": {"browser": true, "es6": true, "node": true}, "settings": {"react": {"version": "detect"}}, "rules": {"import/no-unresolved": 0, "import/prefer-default-export": 0, "indent": ["error", 2, {"SwitchCase": 1}], "no-bitwise": "off", "no-console": "off", "no-unused-vars": "warn", "react/no-unescaped-entities": ["error", {"forbid": [{"char": "'", "alternatives": ["&apos;", "&lsquo;", "&#39;", "&rsquo;"]}, {"char": ">", "alternatives": ["&gt;"]}, {"char": "\"", "alternatives": ["&quot;", "&ldquo;", "&#34;", "&rdquo;"]}, {"char": "}", "alternatives": ["&#125;"]}]}], "semi": ["error", "never"]}}