.datePickerCalendar {
  :global {
    .DayPicker-wrapper {
      padding-bottom: 0;
    }

    .DayPicker-NavButton {
      background-image: none;

      &::after {
        content: '';
        border-bottom: $s-base-1 solid transparent;
        border-top: $s-base-1 solid transparent;
        display: block;
        height: 0;
        margin-top: 4px;
        width: 0;
      }

      &.DayPicker-NavButton--interactionDisabled {
        cursor: default;
        display: block;
        pointer-events: none;

        &,
        &:hover,
        &:focus {
          &.DayPicker-NavButton--prev::after {
            border-right-color: $c-gray2;
          }
          &.DayPicker-NavButton--next::after {
            border-left-color: $c-gray2;
          }
        }
      }

      &.DayPicker-NavButton--prev {
        &::after {
          border-right: 12px solid $c-type-body;
        }
        &:hover {
          &::after {
            border-right-color: $c-green-dark-25;
          }
        }
        &:focus {
          &::after {
            border-right-color: $c-black;
          }
        }
      }

      &.DayPicker-NavButton--next {
        &::after {
          border-left: 12px solid $c-type-body;
        }
        &:hover {
          &::after {
            border-left-color: $c-green-dark-25;
          }
        }
        &:focus {
          &::after {
            border-left-color: $c-black;
          }
        }
      }
    }

    .DayPicker-Caption > div {
      font-weight: 600;
    }

    .DayPicker-Month {
      margin: $s-base-2 0;
    }

    .DayPicker-Day {
      border-radius: 0;
      border: 1px solid $c-gray2;
      height: $s-base-6;
      background-color: $c-white;
      transition: background-color 0.2s;
      width: $s-base-8;
    }

    .DayPicker:not(.DayPicker--interactionDisabled)
      .DayPicker-Day:not(.DayPicker-Day--disabled):not(.DayPicker-Day--selected):not(.DayPicker-Day--outside):hover {
      background-color: $c-gray2;
    }

    .DayPicker-Day--selected:not(.DayPicker-Day--disabled):not(.DayPicker-Day--outside) {
      &,
      &:hover,
      &:focus {
        background-color: $c-green;
        cursor: default;
        font-weight: 700;
      }
    }
  }
}
