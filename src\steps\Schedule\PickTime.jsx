import React from 'react'
import PropTypes from 'prop-types'
import { Field } from 'formik'
import moment from 'moment'

import Loader from '@components/ui/Loader'
import Notice from '@components/ui/Notice'
import { Radio } from '@components/form'

const PickTime = ({ errorMessage, isLoading, openings, selectedDate }) => {
  if (isLoading) {
    return <Loader size={48} />
  }

  if (errorMessage) {
    return <Notice type="error">{errorMessage}</Notice>
  }

  if (selectedDate) {
    const dateFormat = 'YYYY-MM-DD'
    const options = openings.filter(
      (opening) =>
        moment(opening.startTimeISO).format(dateFormat) ===
        moment(selectedDate).format(dateFormat)
    )

    if (options.length === 0) {
      return (
        <Notice type="info">
          Sorry, there are no more openings on this date. Please select a
          different date.
        </Notice>
      )
    }

    return (
      <div
        className="fadeIn"
        style={{
          margin: '0 auto',
          maxWidth: openings.length >= 4 ? '100%' : '480px',
        }}
      >
        <Field
          component={Radio}
          label="Please select an appointment time: "
          maxColumns={options.length >= 4 ? 2 : 1}
          name="schedule"
          options={[
            ...options.map((slot) => ({
              value: `${slot.startTimeISO},${slot.endTimeISO},${slot.hesID},${
                slot.region
              }${
                slot.range
                  ? `,${slot.range}, ${slot.travel_prev}, ${slot.travel_post}`
                  : ''
              },${slot.timezoneOffset}`,
              label: slot.arrivalWindow,
            })),
          ]}
        />
      </div>
    )
  }

  return null
}

PickTime.propTypes = {
  errorMessage: PropTypes.string,
  isLoading: PropTypes.bool,
  openings: PropTypes.array,
  selectedDate: PropTypes.instanceOf(Date),
}

export default PickTime
