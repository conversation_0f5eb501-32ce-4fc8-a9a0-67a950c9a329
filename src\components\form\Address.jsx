import React, { useRef, useEffect } from 'react'
import PropTypes from 'prop-types'
import classNames from 'classnames'
import { get } from 'lodash'
import validateHvacZipCode from '../../utils/validateHvacZipCode'

import ErrorMessage from './ErrorMessage'

import styles from './Input.module.scss'

const Address = ({ field, form, ...props }) => {
  let autocomplete = useRef(null)
  let dropdownRef = useRef(null)
  let inputRef = useRef(null)

  const fieldError = get(form.errors, field.name)
  const fieldTouched = get(form.touched, field.name) || form.submitCount > 0
  const errorAndTouched = fieldError && fieldTouched
  const validAndTouched = !fieldError && fieldTouched

  const handleChange = (e) => {
    if (e.target.name === 'contactInfo.address') props.setAddressValid(false)
    field.onChange(e)
    props.onChange && props.onChange(e.target.value)
  }

  // handleAddressComponents takes in an array of objects returned from the google autocomplete api
  // each object contains a long and short name as well as an array of types. These types while named
  // somewhat untradtionally essentially equate out to house number, street name, postal code, city, etc
  // Here we are looping through the array to create an object of the key/pair values for easier/more dynamic access
  const handleAddressComponents = (components) => {
    const addressComponents = {}
    for (let i = 0; i < components.length; i++) {
      addressComponents[components[i].types[0]] = components[i].long_name
    }
    return addressComponents
  }

  const handlePlaceSelect = async () => {
    const place = autocomplete.current.getPlace()
    let isMaAddress = false
    let isValid = false
    // prettier-ignore
    if (!place || !place.address_components)
      return alert(
        'Error retrieving address information from Google.\n\nPlease try again later or call a HomeWorks Energy Specialist at (781) 305-3319.'
      )
    for (let k = 0; k < place.address_components.length; k++) {
      const comp = place.address_components[k]
      const { long_name, short_name, types } = comp
      if (long_name === 'Massachusetts' && short_name === 'MA') {
        isMaAddress = true
      }
      if (form.values.path === 'hvac' && types.includes('postal_code')) {
        const { short_name } = comp
        const valid = await validateHvacZipCode(short_name)
        isValid = valid
      }
    }
    if (!isMaAddress) {
      return props.setIsMaAddress(isMaAddress)
    }
    if (form.values.path === 'hvac' && !isValid) {
      // Reset validZip so pop-up will occur again
      props.setValidZip(true)
      return props.setValidZip(isValid)
    }
    props.setAddressValid(true)
    form.setFieldValue(field.name, place.formatted_address)
    if (form.values.path === 'hvac') {
      form.setFieldValue(
        props.alt,
        handleAddressComponents(place.address_components)
      )
    }
  }

  const repositionDropdown = () => {
    // Find Google generated dropdown menu (always appended to body)
    dropdownRef.current = window.document.querySelector('body > .pac-container')

    if (inputRef.current && dropdownRef.current) {
      /*
        Move dropdown inside this component so that this component
        becomes its new position anchor
      */
      inputRef.current.parentNode.appendChild(dropdownRef.current)
    }
  }

  useEffect(() => {
    autocomplete.current = new window.google.maps.places.Autocomplete(
      inputRef.current,
      { types: ['address'], componentRestrictions: { country: 'us' } }
    )

    autocomplete.current.addListener('place_changed', handlePlaceSelect)
    if (inputRef.current.value) props.setAddressValid(true)
  }, [])

  useEffect(() => {
    repositionDropdown()
  })

  return (
    <div
      // prettier-ignore
      className={classNames(
        [styles.wrapper],
        { [styles.disabled]: props.disabled },
        { [styles.invalid]: errorAndTouched },
        { [styles.valid]: validAndTouched },
      )}
    >
      {props.label && (
        <label className={styles.label} htmlFor={field.name}>
          {props.label}
        </label>
      )}
      <input
        {...field}
        autoComplete={props.autocomplete}
        autoFocus={props.autofocus}
        className={classNames([styles.field], [styles.text])}
        disabled={props.disabled}
        id={field.name}
        onChange={handleChange}
        placeholder={props.placeholder || ''}
        ref={inputRef}
        type="search"
        value={field.value || ''}
      />

      <ErrorMessage render={errorAndTouched} message={fieldError} />
    </div>
  )
}

// prettier-ignore
Address.propTypes = {
  values: PropTypes.object,
  autocomplete: PropTypes.string,
  autofocus: PropTypes.bool,
  disabled: PropTypes.bool,
  label: PropTypes.string,
  onChange: PropTypes.func,
  placeholder: PropTypes.string,
  field: PropTypes.shape({
    name: PropTypes.string.isRequired,
    value: PropTypes.any,
    onChange: PropTypes.func.isRequired,
    onBlur: PropTypes.func.isRequired,
  }).isRequired,
  alt: PropTypes.string,
  form: PropTypes.shape({
    errors: PropTypes.objectOf(PropTypes.any).isRequired,
    setFieldValue: PropTypes.func.isRequired,
    submitCount: PropTypes.number.isRequired,
    touched: PropTypes.objectOf(PropTypes.any).isRequired,
    values: PropTypes.shape({
      path: PropTypes.string,
      paymentAssistance: PropTypes.string,
    })
  }).isRequired,
  setAddressValid: PropTypes.func.isRequired,
  setIsMaAddress: PropTypes.func.isRequired,
  setValidZip: PropTypes.func.isRequired,
}

Address.defaultProps = {
  autocomplete: 'off',
}

export default Address
