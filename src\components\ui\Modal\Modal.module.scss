/**
  ===========================
  Outer structure
  ===========================
**/

.modal {
  align-items: center;
  background-color: rgba($c-black, 0.6);
  display: flex;
  flex-direction: column;
  height: 100%;
  left: 0;
  overflow: hidden;
  padding: $s-base-2;
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 1072;
}

/**
  ===========================
  Dialog box
  ===========================
**/

.dialog {
  @include slideInTop(0.4s, $s-base-4);
  display: flex;
  flex-basis: auto;
  height: auto;
  margin: auto;
  max-height: 100%;
  max-width: 100%;
  position: relative;

  &.small {
    width: $s-base-46;
  }

  &.medium {
    width: $s-base-32 * 2;
  }

  &.large {
    width: $s-base-54 * 2;
  }
}

.content {
  background-color: $c-white;
  display: flex;
  flex-direction: column;
  max-height: 100%;
  overflow: hidden;
  position: relative;
  width: 100%;
}

/**
  ===========================
  Content
  ===========================
**/

.header {
  margin: $s-base-3 $s-base-3 0 $s-base-3;
  min-height: $s-base-4;
  padding-right: $s-base-6;
  position: relative;

  .title {
    @include t-scale-3();
    font-weight: 600;
    margin-bottom: 0;
    user-select: none;
  }

  .close {
    font-size: $s-base-5;
    font-weight: 600;
    line-height: 1;
    overflow: hidden;
    position: absolute;
    right: 0;
    top: -5px;
    transition: color 0.16s;
    user-select: none;

    &:hover,
    &:focus {
      color: $c-green;
    }
  }
}

.body {
  flex: 1 1 auto;
  margin: $s-base-3 0 $s-base-6 0;
  overflow-y: auto;
  padding: 0 $s-base-3;
  position: relative;
}

.footer {
  margin: 0 $s-base-3 $s-base-3 $s-base-3;
  text-align: right;
}
