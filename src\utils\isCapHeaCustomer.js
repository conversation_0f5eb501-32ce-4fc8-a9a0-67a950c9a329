/**
 * Determines if a customer is a CAP HEA customer
 * CAP HEA customers should generate leads instead of going through the full booking process
 * 
 * @param {Object} values - Form values
 * @returns {boolean} - True if customer is CAP HEA
 */
const isCapHeaCustomer = (values) => {
  return (
    values.path === 'hea' &&
    values.paymentAssistance === 'yes' &&
    values.contactInfo?.source === 'CAP'
  )
}

export default isCapHeaCustomer
