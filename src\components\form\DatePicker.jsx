import React, { useEffect } from 'react'
import PropTypes from 'prop-types'
import Flatpickr from 'flatpickr'
import { get } from 'lodash'
import classNames from 'classnames'
import moment from 'moment'

import ErrorMessage from './ErrorMessage'

import 'flatpickr/dist/flatpickr.min.css'
import styles from './DatePicker.module.scss'

const DatePicker = ({ field, form, ...props }) => {
  let inputEl = React.createRef()
  let flatpickr

  const momentLocale = moment.localeData()

  const fieldError = get(form.errors, field.name)
  const fieldTouched = get(form.touched, field.name) || form.submitCount > 0
  const errorAndTouched = fieldError && fieldTouched
  const validAndTouched = !fieldError && fieldTouched

  const handleChange = (newVal) => {
    if (!newVal || newVal.length === 0) {
      form.setFieldValue(field.name, undefined)
    } else {
      form.setFieldValue(
        field.name,
        props.type === 'single' ? newVal[0] : newVal
      )
    }
  }

  const handleClose = () => {
    form.setFieldTouched(field.name, true)
  }
  useEffect(() => {
    if (inputEl.current) {
      flatpickr = new Flatpickr(inputEl.current, {
        altInput: true,
        defaultDate: field.value,
        disableMobile: true,
        disabled: props.disabledDates,
        enabled: props.enabledDates,
        formatDate: (date) => moment(date).format('L'),
        maxDate: props.disabledAfter,
        minDate: props.disabledBefore,
        mode: props.type,
        onChange: handleChange,
        onClose: handleClose,
        // prettier-ignore
        locale: momentLocale._abbr === 'en'
          ? require('flatpickr/dist/l10n/default.js').default
          : require(`flatpickr/dist/l10n/${momentLocale._abbr}.js`).default[momentLocale._abbr]
      })
      flatpickr
    }
  }, [])

  return (
    <div
      // prettier-ignore
      className={classNames(
        [styles.wrapper],
        { [styles.disabled]: props.disabled },
        { [styles.invalid]: errorAndTouched },
        { [styles.valid]: validAndTouched },
      )}
    >
      {props.label && (
        <label className={styles.label} htmlFor={field.name}>
          {props.label}
        </label>
      )}

      <input
        {...field}
        autoComplete={props.autocomplete}
        autoFocus={props.autofocus}
        className={styles.field}
        disabled={props.disabled}
        id={field.name}
        placeholder={props.placeholder}
        ref={inputEl}
        type="text"
        value={field.value || ''}
      />

      <ErrorMessage render={errorAndTouched} message={fieldError} />
    </div>
  )
}

// prettier-ignore
DatePicker.propTypes = {
  autocomplete: PropTypes.string,
  autofocus: PropTypes.bool,
  disabled: PropTypes.bool,
  disabledAfter: PropTypes.instanceOf(Date),
  disabledBefore: PropTypes.instanceOf(Date),
  disabledDates: PropTypes.arrayOf(PropTypes.instanceOf(Date)),
  enabledDates: PropTypes.arrayOf(PropTypes.instanceOf(Date)),
  label: PropTypes.string,
  placeholder: PropTypes.string,
  type: PropTypes.oneOf(['multiple', 'range', 'single']).isRequired,
  field: PropTypes.shape({
    name: PropTypes.string.isRequired,
    onBlur: PropTypes.func.isRequired,
    onChange: PropTypes.func.isRequired,
    value: PropTypes.any,
  }).isRequired,
  form: PropTypes.shape({
    errors: PropTypes.objectOf(PropTypes.any).isRequired,
    setFieldTouched: PropTypes.func.isRequired,
    setFieldValue: PropTypes.func.isRequired,
    submitCount: PropTypes.number.isRequired,
    touched: PropTypes.objectOf(PropTypes.any).isRequired,
  }).isRequired,
}

DatePicker.defaultProps = {
  autocomplete: 'off',
  type: 'single',
}

export default DatePicker
