import React, { useState } from 'react'
import PropTypes from 'prop-types'
import DayPicker from 'react-day-picker'

import 'react-day-picker/lib/style.css'
import styles from './DatePickerCalendar.module.scss'

const DatePickerCalendar = ({
  monthStart,
  disabledAfter,
  disabledBefore,
  disabledDays,
  disabledDaysOfWeek,
  onChange,
  onMonthChange,
}) => {
  const [selectedDay, setSelectedDay] = useState()

  const handleDayClick = (day, { selected }) => {
    const newValue = selected ? undefined : day

    setSelectedDay(newValue)
    onChange && onChange(newValue)
  }

  const handleMonthChange = (date) => {
    setSelectedDay(null)
    onChange && onChange(null)
    onMonthChange(date)
  }

  return (
    <div className={styles.datePickerCalendar}>
      <DayPicker
        fromMonth={disabledBefore}
        initialMonth={monthStart}
        onDayClick={handleDayClick}
        onMonthChange={handleMonthChange}
        selectedDays={selectedDay}
        toMonth={disabledAfter}
        disabledDays={[
          ...disabledDays,
          { daysOfWeek: disabledDaysOfWeek },
          {
            before: disabledBefore,
            after: disabledAfter,
          },
        ]}
      />
    </div>
  )
}

DatePickerCalendar.propTypes = {
  monthStart: PropTypes.instanceOf(Date),
  disabledAfter: PropTypes.instanceOf(Date),
  disabledBefore: PropTypes.instanceOf(Date),
  disabledDays: PropTypes.arrayOf(PropTypes.instanceOf(Date)),
  disabledDaysOfWeek: PropTypes.arrayOf(PropTypes.number),
  onChange: PropTypes.func.isRequired,
  onMonthChange: PropTypes.func.isRequired,
}

DatePickerCalendar.defaultProps = {
  disabledDays: [],
}

export default DatePickerCalendar
