import React from 'react'
import PropTypes from 'prop-types'

import styles from './Button.module.scss'

const Button = ({
  children,
  className,
  color,
  disabled,
  loading,
  onClick,
  size,
  target,
  to,
  type,
}) => {
  const classesList = [
    styles.button,
    styles[size],
    styles[color],
    disabled ? styles.disabled : null,
    loading ? styles.loading : null,
    className,
  ].join(' ')

  switch (type) {
    case 'anchor':
      return (
        <a
          className={classesList}
          href={to}
          onClick={onClick}
          rel={target === '_blank' ? 'nofollow noopener noreferrer' : undefined}
          target={target}
        >
          {children}
        </a>
      )

    default:
      return (
        <button
          className={classesList}
          disabled={disabled || loading}
          onClick={onClick}
          type={type}
        >
          <div className={styles.text}>{children}</div>
          <div className={styles.spinner} />
        </button>
      )
  }
}

Button.propTypes = {
  children: PropTypes.node.isRequired,
  className: PropTypes.any,
  color: PropTypes.oneOf(['cancel', 'confirm', 'default', 'gray', 'text']),
  disabled: PropTypes.bool,
  loading: PropTypes.bool,
  onClick: PropTypes.func,
  size: PropTypes.oneOf(['small', 'regular', 'large']),
  target: PropTypes.string,
  to: PropTypes.string,
  type: PropTypes.oneOf(['anchor', 'button', 'reset', 'submit']),
}

Button.defaultProps = {
  color: 'default',
  disabled: false,
  loading: false,
  size: 'regular',
  type: 'button',
}

export default Button
