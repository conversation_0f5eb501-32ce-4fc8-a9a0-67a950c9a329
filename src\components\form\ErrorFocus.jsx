// eslint-disable-next-line no-unused-vars
import React, { useEffect } from 'react'
import PropTypes from 'prop-types'

const ErrorFocus = ({ errors, isSubmitting, isValidating }) => {
  useEffect(() => {
    if (isSubmitting && !isValidating) {
      let fieldNames = Object.keys(errors)
      // Contact info returns an object
      if (typeof errors[fieldNames[0]] === 'object') {
        const step = fieldNames[0]
        fieldNames = Object.keys(errors[fieldNames[0]]).map((fieldName) => {
          return `${step}.${fieldName}`
        })
      }
      if (fieldNames.length > 0) {
        const selector = `[id="${fieldNames[0]}"]`
        let errorElement = document.querySelector(selector)
        // errors  object for radio button returns error of field lable and not the actual field
        if (errorElement === null)
          errorElement = document.getElementsByName(fieldNames[0])[0]
        errorElement?.focus()
      }
    }
  }, [errors, isSubmitting, isValidating])

  return null
}

ErrorFocus.propTypes = {
  isSubmitting: PropTypes.any,
  isValidating: PropTypes.any,
  errors: PropTypes.any,
}

export default ErrorFocus
