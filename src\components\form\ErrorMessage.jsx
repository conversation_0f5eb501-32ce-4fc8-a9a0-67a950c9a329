import React from 'react'
import PropTypes from 'prop-types'
import classNames from 'classnames'

import styles from './ErrorMessage.module.scss'

const ErrorMessage = ({ centered, message, render }) => {
  if (message && render) {
    // prettier-ignore
    const parsedMessage = typeof message === 'object'
      ? Object.values(message).join(', ')
      : message

    return (
      <div
        // prettier-ignore
        className={classNames(
          [styles.error],
          { 't-center': centered }
        )}
        title={parsedMessage}
      >
        {parsedMessage}
      </div>
    )
  }

  return null
}

ErrorMessage.propTypes = {
  centered: PropTypes.bool,
  message: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
  render: PropTypes.bool,
}

export default ErrorMessage
