{"name": "online-scheduling-tool", "version": "1.0.0", "private": true, "author": "HomeWorks Energy <<EMAIL>> (https://www.homeworksenergy.com)", "license": "UNLICENSED", "repository": {"type": "git", "url": "**************:HomeWorksEnergy/online-scheduling-tool.git"}, "bugs": {"url": "https://github.com/HomeWorksEnergy/online-scheduling-tool/issues"}, "homepage": "https://github.com/HomeWorksEnergy/online-scheduling-tool#readme", "browserslist": ["> 5%", "IE 11"], "scripts": {"test": "echo \"error: no test task configured\"", "test:watch": "echo \"error: no test:watch task configured\"", "dev": "webpack-dev-server --env development", "build": "webpack --env production", "deploy:test": "npm run build && aws s3 rm s3://hostest.homeworksenergy.com/ --recursive && aws s3 cp ./public s3://hostest.homeworksenergy.com/ --recursive --cache-control 'max-age=1'", "deploy:production": "npm run build && aws s3 rm s3://hos.homeworksenergy.com/ --recursive && aws s3 cp ./public s3://hos.homeworksenergy.com/ --recursive --cache-control 'max-age=1'"}, "devDependencies": {"@babel/core": "^7.7.7", "@babel/plugin-proposal-class-properties": "^7.7.4", "@babel/plugin-proposal-object-rest-spread": "^7.7.7", "@babel/plugin-proposal-optional-chaining": "^7.7.5", "@babel/plugin-syntax-dynamic-import": "^7.7.4", "@babel/preset-env": "^7.7.7", "@babel/preset-react": "^7.7.4", "@svgr/webpack": "^5.0.1", "autoprefixer": "^9.7.3", "babel-eslint": "^10.0.3", "babel-loader": "^8.0.6", "clean-webpack-plugin": "^3.0.0", "core-js": "^3.6.2", "css-loader": "^3.4.1", "cssnano": "^4.1.10", "eslint": "^6.8.0", "eslint-config-prettier": "^6.9.0", "eslint-loader": "^3.0.3", "eslint-plugin-prettier": "^3.1.2", "eslint-plugin-react": "^7.17.0", "favicons-webpack-plugin": "^2.1.0", "file-loader": "^5.0.2", "html-webpack-plugin": "^3.2.0", "html-webpack-template": "^6.2.0", "image-webpack-loader": "^6.0.0", "mini-css-extract-plugin": "^0.9.0", "node-sass": "^4.13.0", "optimize-css-assets-webpack-plugin": "^5.0.3", "postcss-loader": "^3.0.0", "prettier": "^1.19.1", "regenerator-runtime": "^0.13.3", "sass": "^1.62.1", "sass-loader": "^8.0.2", "style-loader": "^1.1.2", "terser-webpack-plugin": "^2.3.1", "webpack": "^4.41.5", "webpack-cli": "^3.3.10", "webpack-dev-server": "^3.10.1", "webpack-merge": "^4.2.2"}, "dependencies": {"axios": "^0.19.1", "classnames": "^2.2.6", "flat": "^5.0.0", "flatpickr": "^4.6.3", "formik": "^1.5.8", "lodash": "^4.17.15", "moment": "^2.24.0", "prop-types": "^15.7.2", "react": "^16.12.0", "react-day-picker": "^7.4.0", "react-dom": "^16.12.0", "react-text-mask": "^5.4.3", "yup": "^0.28.0"}}