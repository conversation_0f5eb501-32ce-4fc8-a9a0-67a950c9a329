/**
  ===========================
  Structure
  ===========================
**/

.wrapper {
  margin-bottom: $s-base-5;
  position: relative;
  &:last-child {
    margin-bottom: 0;
  }
}

.label {
  display: block;
  margin-bottom: $s-base-1;
  font-weight: 600;
  user-select: none;
  @include breakpoint(above, $phablet) {
    margin-right: $s-base-3;
  }
}

.field {
  border-radius: $border-radius;
  border: 1px solid $c-gray3;
  display: block;
  padding: ($s-base-1 - 1px) $s-base-6 ($s-base-1 - 1px) $s-base-3;
  transition: border 0.16s;
  width: 100%;

  &:hover {
    border-color: $c-gray4;
  }

  &:focus {
    border-color: $c-green-dark-25;
    box-shadow: 0px 0px 0px 2px rgba($c-green, 0.3);
  }
}

.field {
  &::-webkit-calendar-picker-indicator {
    display: none;
  }
}

.field.number {
  -moz-appearance: textfield;
  &::-webkit-inner-spin-button,
  &::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
}

/**
  ===========================
  States
  ===========================
**/

.disabled .field {
  background-color: $c-gray3;
  border-color: $c-gray3;
  cursor: not-allowed;
}

// .invalid .field,
// .valid .field {
//   background-color: $c-gray1;
//   background-repeat: no-repeat;
// }

.invalid .field {
  background-image: url('./errormark.svg');
  background-position: calc(100% - #{$s-base-3}) 12px;
  background-size: 18px;
  border-color: $c-danger;

  &:focus {
    box-shadow: 0px 0px 0px 2px rgba($c-danger, 0.3);
  }
}

.valid .field {
  background-image: url('./checkmark.svg');
  background-position: calc(100% - #{$s-base-3}) 9px;
  background-size: 14px;
}

/*
  ==========================
  Address
  ==========================
*/

:global {
  .pac-container {
    top: 100% !important;
    left: 0 !important;
  }
  #unitNumber1,
  #unitNumber2,
  #unitNumber3,
  #unitNumber4 {
    width: 35%;
  }
}
