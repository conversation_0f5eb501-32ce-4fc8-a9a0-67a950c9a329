/**
  ===========================
  Colours
  ===========================
**/

// Site palette
$c-green: #99c543;
$c-green-light-15: tint($c-green, 15%);
$c-green-light-25: tint($c-green, 25%);
$c-green-light-50: tint($c-green, 50%);
$c-green-dark-25: shade($c-green, 25%);
$c-green-dark-50: shade($c-green, 50%);

// Green for color blind users
$cb-green: #7b903a;

$c-blue: #4675ad;
$c-blue-light-15: tint($c-blue, 15%);
$c-blue-light-25: tint($c-blue, 25%);
$c-blue-light-50: tint($c-blue, 50%);
$c-blue-dark-25: shade($c-blue, 25%);
$c-blue-dark-50: shade($c-blue, 50%);

$c-orange: #f2733b;
$c-orange-light-15: tint($c-orange, 15%);
$c-orange-light-25: tint($c-orange, 25%);
$c-orange-light-50: tint($c-orange, 50%);
$c-orange-dark-25: shade($c-orange, 25%);
$c-orange-dark-50: shade($c-orange, 50%);
$c-orange-dark-80: shade($c-orange, 80%);

$c-red: #f13b3b;
$c-red-light-15: tint($c-red, 15%);
$c-red-light-25: tint($c-red, 25%);
$c-red-light-50: tint($c-red, 50%);
$c-red-dark-25: shade($c-red, 25%);
$c-red-dark-50: shade($c-red, 50%);
$c-red-dark-80: shade($c-red, 80%);

$c-gray0: #fbfbfa;
$c-gray1: #f2f2f2;
$c-gray2: #e6e6e6;
$c-gray3: #8d8b7e;
$c-gray4: #6e6c62;

// Semantic
$c-success: #28a745;
$c-success-light-50: tint($c-success, 50%);
$c-success-light-75: tint($c-success, 75%);
$c-success-dark-25: shade($c-success, 25%);
$c-success-dark-50: shade($c-success, 50%);

$c-info: #17a2b8;
$c-info-light-50: tint($c-info, 50%);
$c-info-light-75: tint($c-info, 75%);
$c-info-dark-25: shade($c-info, 25%);
$c-info-dark-50: shade($c-info, 50%);

$c-warning: #ff9800;
$c-warning-light-50: tint($c-warning, 50%);
$c-warning-light-75: tint($c-warning, 75%);
$c-warning-dark-25: shade($c-warning, 25%);
$c-warning-dark-50: shade($c-warning, 50%);

$c-danger: #dc3545;
$c-danger-light-50: tint($c-danger, 50%);
$c-danger-light-75: tint($c-danger, 75%);
$c-danger-dark-25: shade($c-danger, 25%);
$c-danger-dark-50: shade($c-danger, 50%);

// HomeWorks Green
$c-custom1: #99c340;

// Utility
$c-dark: #161616;
$c-dark-15: tint($c-dark, 15%);
$c-dark-25: tint($c-dark, 25%);
$c-dark-50: tint($c-dark, 50%);

$c-light: #f8f8f8;
$c-light-half: tint($c-light, 50%);
$c-light-5: shade($c-light, 5%);
$c-light-15: shade($c-light, 15%);
$c-light-25: shade($c-light, 25%);
$c-light-50: shade($c-light, 50%);

// Basic
$c-white: #ffffff;
$c-black: #000000;
$c-transparent: rgba(#ffffff, 0);

/**
  ===========================
  Typography
  ===========================
**/

// Font sizes
$t-mobile-base-size: 15px;
$t-desktop-base-size: 16px;

// Font families
$t-font-openSans: 'Open Sans', sans-serif;

// Colors
$c-type-headings: #008afc;
$c-type-body: $c-gray4;
$c-link: $c-type-headings;
$c-link-hover: shade($c-type-headings, 50%);

$c-type-headings-inverted: $c-white;
$c-type-body-inverted: $c-white;
$c-link-inverted: $c-white;
$c-link-hover-inverted: shade($c-white, 25%);

$c-type-muted: rgba($c-type-body, 0.8);

/**
  ===========================
  Miscellaneous settings
  ===========================
**/

$border-radius: 24px;
