import React from 'react'
import PropTypes from 'prop-types'

import Button from '@components/ui/Button'
import Modal from '@components/ui/Modal'
import goPrevStep from '@utils/goPrevStep'
import { HelpBody, HelpFooter } from './Help'

import { ReactComponent as IconHelp } from '@assets/icons/help.svg'

import styles from './Footer.module.scss'

const Footer = ({
  currentStep,
  currentStepIndex,
  isSubmitting,
  setCurrentStepIndex,
  stepsLength,
  enableSubmitButton,
  values,
}) => {
  const isFirstStep = currentStepIndex === 0
  const isLastStep = currentStepIndex === stepsLength - 1
  const isSubmitStep = currentStep.info.isSubmitStep

  return (
    <footer className={styles.footer}>
      <div className={styles.navigation}>
        {!isFirstStep && !isLastStep && (
          <Button
            color="gray"
            disabled={isSubmitting}
            size="large"
            type="button"
            onClick={() => goPrevStep(setCurrentStepIndex, values)}
            className={styles.prevBtn}
          >
            &laquo;
          </Button>
        )}

        {!isLastStep && !isSubmitStep && (
          <Button
            color="default"
            loading={isSubmitting}
            size="large"
            className={styles.nextBtn}
            type="submit"
          >
            Next Step
          </Button>
        )}

        {isSubmitStep && (
          <Button
            color="confirm"
            className={styles.nextBtn}
            loading={isSubmitting}
            size="large"
            type="submit"
            disabled={!enableSubmitButton}
          >
            Submit
          </Button>
        )}

        {isLastStep && (
          <Button
            color="default"
            size="large"
            target="_self"
            to="https://homeworksenergy.com"
            type="anchor"
          >
            Return to HomeWorksEnergy.com
          </Button>
        )}
      </div>

      <div className={styles.actions}>
        <Modal
          title="Help &amp; Answers"
          size="small"
          trigger={(toggleModal) => (
            <button
              className={styles.helpToggle}
              type="button"
              onClick={() => toggleModal()}
            >
              <IconHelp />
            </button>
          )}
          body={() => <HelpBody />}
          footer={(toggleModal) => <HelpFooter toggleModal={toggleModal} />}
        />
      </div>
    </footer>
  )
}

Footer.propTypes = {
  currentStep: PropTypes.func.isRequired,
  currentStepIndex: PropTypes.number.isRequired,
  isSubmitting: PropTypes.bool.isRequired,
  setCurrentStepIndex: PropTypes.func.isRequired,
  stepsLength: PropTypes.number.isRequired,
  enableSubmitButton: PropTypes.bool.isRequired,
  values: PropTypes.shape({ schedule: PropTypes.string }).isRequired,
}

export default Footer
