import React, { useEffect } from 'react'
import PropTypes from 'prop-types'
import * as yup from 'yup'
import { Field } from 'formik'

import Intro from '@components/ui/Intro'
import { Radio } from '@components/form'
import urlPathSetter from '@utils/urlPathSetter'
import styles from '@components/form/Radio.module.scss'
import getURLParams from '@utils/getURLParams'

const ChoosePath = ({ currentStepIndex, setValidationSchema }) => {
  urlPathSetter()
  const paramsObj = getURLParams()
  let options = {
    path: [
      {
        value: 'hea',
        // This label is here to put the registered entity into text
        label: (
          <>
            Mass Save
            <span className={styles.trademark}>
              {String.fromCharCode(174)}
            </span>{' '}
            Home Energy Assessment
          </>
        ),
      },
    ],
  }
  if (
    !paramsObj.utm_campaign ||
    (paramsObj.utm_campaign && !paramsObj.utm_campaign.includes('HVAC'))
  ) {
    options.path = [
      ...options.path,
      { value: 'hvac', label: 'Heating and Cooling Visit' },
    ]
  }

  useEffect(() => {
    setValidationSchema(
      yup.object().shape({
        path: yup
          .string()
          .oneOf(options.path.map(({ value }) => value))
          .required('This field is required.'),
      })
    )
  }, [currentStepIndex])

  return (
    <div className="wrapper-s flex-column">
      <Intro title="What brings you here today?"></Intro>

      <Field
        component={Radio}
        maxColumns={1}
        name="path"
        options={options.path}
      />
    </div>
  )
}

ChoosePath.info = {
  title: 'Choose Service',
  key: 'choosePath',
}

ChoosePath.propTypes = {
  currentStepIndex: PropTypes.number.isRequired,
  setFieldValue: PropTypes.func.isRequired,
  setValidationSchema: PropTypes.func.isRequired,
  values: PropTypes.object.isRequired,
}

export default ChoosePath
