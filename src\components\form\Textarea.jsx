import React from 'react'
import PropTypes from 'prop-types'
import { get } from 'lodash'
import classNames from 'classnames'

import ErrorMessage from './ErrorMessage'

import styles from './Textarea.module.scss'

const Textarea = ({ field, form, ...props }) => {
  const fieldError = get(form.errors, field.name)
  const fieldTouched = get(form.touched, field.name) || form.submitCount > 0
  const errorAndTouched = fieldError && fieldTouched
  const validAndTouched = !fieldError && fieldTouched

  const handleInput = (e) => {
    if (props.autoresize) {
      const offset = e.target.offsetHeight - e.target.clientHeight
      e.target.style.height = 'auto'
      e.target.style.height = e.target.scrollHeight + offset + 'px'
    }
  }

  const handleChange = (e) => {
    field.onChange(e)
    props.onChange && props.onChange(e.target.value)
  }

  return (
    <div
      // prettier-ignore
      className={classNames(
        [styles.wrapper],
        { [styles.disabled]: props.disabled },
        { [styles.invalid]: errorAndTouched },
        { [styles.valid]: validAndTouched },
      )}
    >
      {props.label && (
        <label className={styles.label} htmlFor={field.name}>
          {props.label}
        </label>
      )}

      <textarea
        {...field}
        autoComplete={props.autocomplete}
        autoFocus={props.autofocus}
        className={styles.field}
        disabled={props.disabled}
        id={field.name}
        maxLength={props.max}
        minLength={props.min}
        onChange={handleChange}
        onInput={handleInput}
        placeholder={props.placeholder}
        rows={3}
        value={field.value || ''}
      />

      <ErrorMessage render={errorAndTouched} message={fieldError} />
    </div>
  )
}

// prettier-ignore
Textarea.propTypes = {
  autocomplete: PropTypes.string,
  autofocus: PropTypes.bool,
  autoresize: PropTypes.bool,
  disabled: PropTypes.bool,
  label: PropTypes.string,
  max: PropTypes.number,
  min: PropTypes.number,
  onChange: PropTypes.func,
  placeholder: PropTypes.string,
  field: PropTypes.shape({
    name: PropTypes.string.isRequired,
    value: PropTypes.any,
    onChange: PropTypes.func.isRequired,
    onBlur: PropTypes.func.isRequired,
  }).isRequired,
  form: PropTypes.shape({
    errors: PropTypes.objectOf(PropTypes.any).isRequired,
    submitCount: PropTypes.number.isRequired,
    touched: PropTypes.objectOf(PropTypes.any).isRequired,
  }).isRequired,
}

Textarea.defaultProps = {
  autocomplete: 'off',
  autoresize: true,
}

export default Textarea
