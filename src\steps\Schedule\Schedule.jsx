import React, { useEffect, useState } from 'react'
import PropTypes from 'prop-types'
import * as yup from 'yup'
import moment from 'moment'

import Intro from '@components/ui/Intro'
import PickDate from './PickDate'
import PickTime from './PickTime'
import getOpenings from '@utils/getOpenings'

import urlPathSetter from '@utils/urlPathSetter'

const Schedule = ({
  currentStepIndex,
  setValidationSchema,
  values,
  setEnableSubmitButton,
}) => {
  values.step = 'schedule'
  urlPathSetter(values)
  const [openings, setOpenings] = useState([])
  const [disabledDays, setDisabledDays] = useState([])
  const [startDay, setStartDay] = useState(new Date())
  const [isLoading, setIsLoading] = useState(false)
  const [selectedDate, setSelectedDate] = useState(undefined)
  const [errorMessage, setErrorMessage] = useState(undefined)
  const { path } = values

  const handleGetOpenings = async (newDate) => {
    const address = values.contactInfo.address
    const program = values.gasProvider
      ? values.gasProvider.replace(' ', '')
      : values.electricProvider
    const monthEnd = moment(startDay).endOf('month')
    setIsLoading(true)
    setErrorMessage(undefined)

    try {
      const openings = await getOpenings(
        newDate,
        monthEnd,
        address,
        program,
        values
      )
      setOpenings(openings)
    } catch (error) {
      setErrorMessage(error.message)
    }

    setIsLoading(false)
  }

  const handleDisabledDays = () => {
    const dateFormat = 'YYYY-MM-DD'
    const daysOfMonth = {}
    const currentDay = moment(startDay)
    const lastDay = moment(startDay).endOf('month')
    // Add all days of the month to an object with key 'YYYY-MM-DD'
    while (currentDay.isSameOrBefore(lastDay)) {
      daysOfMonth[currentDay.format(dateFormat)] = moment(currentDay).toDate()
      currentDay.add(1, 'Days')
    }
    const daysWithOpenings = []
    // Create an array of 'YYYY-MM-DD' values for each day with openings
    for (let i = 0; i < openings.length; i++) {
      const dateKey = moment(openings[i].startTimeISO).format(dateFormat)
      if (!daysWithOpenings.includes(dateKey)) daysWithOpenings.push(dateKey)
    }
    // For each day with openings, delete from days of month,
    // leaving only the days that should be disabled
    daysWithOpenings.forEach((day) => {
      delete daysOfMonth[day]
    })
    setDisabledDays(Object.values(daysOfMonth))
  }

  useEffect(() => {
    setValidationSchema(
      yup.object().shape({
        schedule: yup.string().required('This field is required'),
      })
    )
  }, [currentStepIndex])

  useEffect(() => {
    handleGetOpenings(startDay)
  }, [startDay])

  useEffect(() => {
    handleDisabledDays()
  }, [openings])

  useEffect(() => {
    if (selectedDate == null) setEnableSubmitButton(false)
    else setEnableSubmitButton(true)
  }, [selectedDate])

  const isCapAgency =
    values?.paymentAssistance === 'yes' && values?.contactInfo?.source === 'CAP'
  const isCap = values?.paymentAssistance === 'yes'
  const isSingleFamilyMarketRate =
    values?.paymentAssistance === 'no' &&
    values?.singleOrMulti === 'Single family'

  return (
    <div className="wrapper-s">
      <div className="t-center">
        <Intro title="Schedule" centered>
          {/* prettier-ignore */}
          <p>{`We estimate your ${path === 'hea' ? 'energy assessment' : 'appointment'} will take 2-3 hours.`}</p>
        </Intro>

        <PickDate
          monthStart={startDay}
          handleDateChange={setSelectedDate}
          handleMonthChange={setStartDay}
          disabledDays={disabledDays}
          isLoading={isLoading}
          isCap={isCap}
          isCapAgency={isCapAgency}
          isSingleFamilyMarketRate={isSingleFamilyMarketRate}
          path={path}
        />

        <br />
        <br />

        <PickTime
          errorMessage={errorMessage}
          openings={openings}
          selectedDate={selectedDate}
        />
      </div>
    </div>
  )
}

Schedule.info = {
  title: 'Schedule',
  key: 'schedule',
  isSubmitStep: true,
}

Schedule.propTypes = {
  currentStepIndex: PropTypes.number.isRequired,
  setValidationSchema: PropTypes.func.isRequired,
  values: PropTypes.object.isRequired,
  setEnableSubmitButton: PropTypes.func.isRequired,
}

export default Schedule
