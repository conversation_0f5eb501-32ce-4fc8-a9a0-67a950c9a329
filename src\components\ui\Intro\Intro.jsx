import React from 'react'
import PropTypes from 'prop-types'
import classNames from 'classnames'

import styles from './Intro.module.scss'

const Intro = ({ centered, children, title }) => (
  <div className={classNames([styles.intro], { [styles.centered]: centered })}>
    <h1 className={styles.title}>{title}</h1>
    {children && children}
  </div>
)

Intro.propTypes = {
  centered: PropTypes.bool,
  children: PropTypes.node,
  title: PropTypes.string.isRequired,
}

export default Intro
