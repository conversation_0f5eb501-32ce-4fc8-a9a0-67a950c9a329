import React from 'react'
import PropTypes from 'prop-types'

import Button from '@components/ui/Button'

const RedirectFooter = ({ toggleModal, redirectUrl, buttonTitle }) => {
  const redirect = (url) => {
    history.pushState(null, null, '/calendly')
    window.location.href = url
  }
  return (
    <div className="t-center">
      <Button
        onClick={() => {
          toggleModal()
          redirect(redirectUrl)
        }}
      >
        {buttonTitle}
      </Button>
    </div>
  )
}

RedirectFooter.propTypes = {
  toggleModal: PropTypes.func.isRequired,
  redirectUrl: PropTypes.string.isRequired,
  buttonTitle: PropTypes.string,
}

RedirectFooter.defaultProps = {
  buttonTitle: 'Redirect',
}

export default RedirectFooter
