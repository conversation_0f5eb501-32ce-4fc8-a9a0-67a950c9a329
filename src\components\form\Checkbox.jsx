import React from 'react'
import PropTypes from 'prop-types'
import { get } from 'lodash'
import classNames from 'classnames'

import ErrorMessage from './ErrorMessage'

import styles from './Checkbox.module.scss'

const Checkbox = ({ field, form, ...props }) => {
  const fieldError = get(form.errors, field.name)
  const fieldTouched = get(form.touched, field.name) || form.submitCount > 0
  const errorAndTouched = fieldError && fieldTouched
  const validAndTouched = !fieldError && fieldTouched

  const handleChange = (e) => {
    field.onChange(e)
    field.onBlur(e)
    props.onChange && props.onChange(e.target.value)
  }

  return (
    <div
      // prettier-ignore
      className={classNames(
        [styles.wrapper],
        { [styles.disabled]: props.disabled },
        { [styles.checked]: field.value || false },
        { [styles.invalid]: errorAndTouched },
        { [styles.valid]: validAndTouched },
      )}
    >
      <input
        {...field}
        autoComplete={props.autocomplete}
        checked={field.value || false}
        className={styles.field}
        disabled={props.disabled}
        id={field.name}
        onChange={handleChange}
        type="checkbox"
      />

      <label className={styles.controller} htmlFor={field.name}>
        <span className={styles.label}>
          {props.label}
          {props.showPrivacyPolicy && (
            <a
              href="https://www.homeworksenergy.com/privacy-policy/"
              target="_blank"
              rel="noopener noreferrer"
            >
              Terms & Conditions and Privacy Policy
            </a>
          )}
        </span>
      </label>

      <ErrorMessage render={errorAndTouched} message={fieldError} />
    </div>
  )
}

// prettier-ignore
Checkbox.propTypes = {
  autocomplete: PropTypes.string,
  disabled: PropTypes.bool,
  label: PropTypes.string.isRequired,
  onChange: PropTypes.func,
  field: PropTypes.shape({
    name: PropTypes.string.isRequired,
    value: PropTypes.any,
    onChange: PropTypes.func.isRequired,
    onBlur: PropTypes.func.isRequired,
  }).isRequired,
  form: PropTypes.shape({
    errors: PropTypes.objectOf(PropTypes.any).isRequired,
    submitCount: PropTypes.number.isRequired,
    touched: PropTypes.objectOf(PropTypes.any).isRequired,
  }).isRequired,
  showPrivacyPolicy: PropTypes.bool
}

Checkbox.defaultProps = {
  autocomplete: 'off',
  variant: 'checkbox',
  showPrivacyPolicy: false,
}

export default Checkbox
