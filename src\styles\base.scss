@import './variables.scss';
/**
  ===========================
  Sizes
  ===========================
**/

$s-base-1: 8px;
$s-base-2: $s-base-1 * 2; // 16px
$s-base-3: $s-base-1 * 3; // 24px
$s-base-4: $s-base-1 * 4; // 32px
$s-base-5: $s-base-1 * 5; // 40px
$s-base-6: $s-base-1 * 6; // 48px
$s-base-8: $s-base-1 * 8; // 64px
$s-base-10: $s-base-1 * 10; // 80px
$s-base-12: $s-base-1 * 12; // 96px
$s-base-14: $s-base-1 * 14; // 112px
$s-base-16: $s-base-1 * 16; // 128px
$s-base-18: $s-base-1 * 18; // 144px
$s-base-20: $s-base-1 * 20; // 160px
$s-base-22: $s-base-1 * 22; // 176px
$s-base-24: $s-base-1 * 24; // 192px
$s-base-26: $s-base-1 * 26; // 208px
$s-base-28: $s-base-1 * 28; // 224px
$s-base-30: $s-base-1 * 30; // 240px
$s-base-32: $s-base-1 * 32; // 256px
$s-base-34: $s-base-1 * 34; // 272px
$s-base-36: $s-base-1 * 36; // 288px
$s-base-38: $s-base-1 * 38; // 304px
$s-base-40: $s-base-1 * 40; // 320px
$s-base-42: $s-base-1 * 42; // 336px
$s-base-44: $s-base-1 * 44; // 352px
$s-base-46: $s-base-1 * 46; // 368px
$s-base-48: $s-base-1 * 48; // 384px
$s-base-50: $s-base-1 * 50; // 400px
$s-base-52: $s-base-1 * 52; // 416px
$s-base-54: $s-base-1 * 54; // 432px
$s-base-56: $s-base-1 * 56; // 448px
$s-base-58: $s-base-1 * 58; // 464px

$phablet: 560px;
$tablet: 800px;
$laptop: 1280px;
$desktop: 1440px;

/**
  ===========================
  Typography scale
  ===========================
**/

// Scale ratios
$t-ratio-mobile: 1.1;
$t-ratio-desktop: 1.2;

// Size map mobile
$t-size-mobile-0: round($t-mobile-base-size / $t-ratio-mobile);
$t-size-mobile-1: $t-mobile-base-size;
$t-size-mobile-2: round($t-size-mobile-1 * $t-ratio-mobile);
$t-size-mobile-3: round($t-size-mobile-2 * $t-ratio-mobile);
$t-size-mobile-4: round($t-size-mobile-3 * $t-ratio-mobile);
$t-size-mobile-5: round($t-size-mobile-4 * $t-ratio-mobile);
$t-size-mobile-6: round($t-size-mobile-5 * $t-ratio-mobile);
$t-size-mobile-7: round($t-size-mobile-6 * $t-ratio-mobile);
$t-size-mobile-8: round($t-size-mobile-7 * $t-ratio-mobile);

// Size map desktop
$t-size-desktop-0: round($t-desktop-base-size / $t-ratio-desktop);
$t-size-desktop-1: $t-desktop-base-size;
$t-size-desktop-2: round($t-size-desktop-1 * $t-ratio-desktop);
$t-size-desktop-3: round($t-size-desktop-2 * $t-ratio-desktop);
$t-size-desktop-4: round($t-size-desktop-3 * $t-ratio-desktop);
$t-size-desktop-5: round($t-size-desktop-4 * $t-ratio-desktop);
$t-size-desktop-6: round($t-size-desktop-5 * $t-ratio-desktop);
$t-size-desktop-7: round($t-size-desktop-6 * $t-ratio-desktop);
$t-size-desktop-8: round($t-size-desktop-7 * $t-ratio-desktop);

// Calculate font size and nearest line height divisible by 8
@mixin fontCalc($fontSizeMobile, $fontSizeDesktop, $withLineHeight) {
  font-size: $fontSizeMobile;
  @if $withLineHeight == true {
    line-height: ((ceil($fontSizeMobile / 8) + 1) * 8 / $fontSizeMobile);
  } @else {
    line-height: 1;
  }
  @include breakpoint(above, $tablet) {
    font-size: $fontSizeDesktop;
    @if $withLineHeight == true {
      line-height: ((ceil($fontSizeDesktop / 8) + 1) * 8 / $fontSizeDesktop);
    } @else {
      line-height: 1;
    }
  }
}

// Responsive size mixins
@mixin t-scale-0($withLineHeight: true) {
  @include fontCalc($t-size-mobile-0, $t-size-desktop-0, $withLineHeight);
}
@mixin t-scale-1($withLineHeight: true) {
  @include fontCalc($t-size-mobile-1, $t-size-desktop-1, $withLineHeight);
}

@mixin t-scale-2($withLineHeight: true) {
  @include fontCalc($t-size-mobile-2, $t-size-desktop-2, $withLineHeight);
}
@mixin t-scale-3($withLineHeight: true) {
  @include fontCalc($t-size-mobile-3, $t-size-desktop-3, $withLineHeight);
}

@mixin t-scale-4($withLineHeight: true) {
  @include fontCalc($t-size-mobile-4, $t-size-desktop-4, $withLineHeight);
}
@mixin t-scale-5($withLineHeight: true) {
  @include fontCalc($t-size-mobile-5, $t-size-desktop-5, $withLineHeight);
}

@mixin t-scale-6($withLineHeight: true) {
  @include fontCalc($t-size-mobile-6, $t-size-desktop-6, $withLineHeight);
}
@mixin t-scale-7($withLineHeight: true) {
  @include fontCalc($t-size-mobile-7, $t-size-desktop-7, $withLineHeight);
}

@mixin t-scale-8($withLineHeight: true) {
  @include fontCalc($t-size-mobile-8, $t-size-desktop-8, $withLineHeight);
}

/**
  ===========================
  Functions and Mixins
  ===========================
**/

@function tint($color, $percent) {
  @return mix(#ffffff, $color, $percent);
}

@function shade($color, $percent) {
  @return mix(#000000, $color, $percent);
}

@mixin breakpoint($trigger, $breakpoint) {
  @if $trigger == above {
    @media only screen and (min-width: $breakpoint) {
      @content;
    }
  } @else if $trigger == below {
    @media only screen and (max-width: $breakpoint - 1) {
      @content;
    }
  }
}

@mixin elevation($level) {
  @if $level == 0 {
    box-shadow: none;
  } @else if $level == 1 {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  } @else if $level == 2 {
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
  } @else if $level == 3 {
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);
  } @else if $level == 4 {
    box-shadow: 0 14px 28px rgba(0, 0, 0, 0.25), 0 10px 10px rgba(0, 0, 0, 0.22);
  } @else if $level == 5 {
    box-shadow: 0 19px 38px rgba(0, 0, 0, 0.3), 0 15px 12px rgba(0, 0, 0, 0.22);
  }
}
