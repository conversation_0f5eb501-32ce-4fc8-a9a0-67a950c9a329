/**
  ===========================
  Structure
  ===========================
**/

.wrapper {
  margin-bottom: $s-base-5;
  position: relative;
  &:last-child {
    margin-bottom: 0;
  }
}

.label {
  display: block;
  margin-bottom: $s-base-1;
  user-select: none;
  @include breakpoint(above, $phablet) {
    margin-left: $s-base-3;
    margin-right: $s-base-3;
  }
}

.field {
  background-color: $c-gray1;
  background-image: url('./calendar.svg');
  background-position: calc(100% - 21px) 8px;
  background-repeat: no-repeat;
  background-size: 21px;
  border-radius: $border-radius;
  border: 1px solid $c-gray3;
  cursor: pointer;
  display: block;
  padding: ($s-base-1 - 1px) $s-base-10 ($s-base-1 - 1px) $s-base-3;
  transition: border 0.16s;
  width: 100%;

  &:hover {
    border-color: $c-gray4;
  }

  &:focus {
    border-color: $c-green-dark-25;
    box-shadow: 0px 0px 0px 2px rgba($c-green, 0.3);
  }
}

.field {
  &::-webkit-calendar-picker-indicator {
    display: none;
  }
}

/**
  ===========================
  States
  ===========================
**/

.disabled .field {
  background-color: $c-gray3;
  border-color: $c-gray3;
  cursor: not-allowed;
}

.invalid .field,
.valid .field {
  background-color: $c-gray1;
  background-repeat: no-repeat;
}

.invalid .field {
  background-image: url('./errormark.svg'), url('./calendar.svg');
  // prettier-ignore
  background-position:
    calc(100% - #{$s-base-6}) 12px,
    calc(100% - 21px) 8px;
  background-size: 18px, 21px;
  border-color: $c-danger;

  &:focus {
    box-shadow: 0px 0px 0px 2px rgba($c-danger, 0.3);
  }
}

.valid .field {
  background-image: url('./checkmark.svg'), url('./calendar.svg');
  // prettier-ignore
  background-position:
    calc(100% - #{$s-base-6}) 9px,
    calc(100% - 21px) 8px;
  background-size: 14px, 21px;
}

/**
  ===========================
  Calendar popup
  ===========================
**/

:global {
  .flatpickr-calendar {
    @include elevation(3);
    @include breakpoint(above, $tablet) {
      margin: 4px $s-base-3 0 $s-base-3;
    }

    .flatpickr-month {
      @include breakpoint(below, $tablet) {
        height: $s-base-5;
      }
    }

    .flatpickr-prev-month,
    .flatpickr-next-month {
      @include breakpoint(below, $tablet) {
        height: $s-base-5;
        padding: 14px 10px;
      }

      &:hover svg {
        fill: $c-blue-light-25;
      }
    }

    .flatpickr-monthDropdown-months,
    .numInput.cur-year {
      border: 1px solid $c-gray3 !important;
      margin: 0;
      padding: 0 4px;
      @include breakpoint(below, $tablet) {
        font-size: $s-base-2;
        height: $s-base-4;
        line-height: $s-base-4;
      }
      @include breakpoint(above, $tablet) {
        font-size: $s-base-2;
        height: $s-base-3;
        line-height: $s-base-3;
      }
    }
    .numInputWrapper {
      position: relative;
      @include breakpoint(above, $tablet) {
        top: -1px;
      }
    }

    .dayContainer {
      border-left: 1px solid $c-gray2;
      border-top: 1px solid $c-gray2;
      justify-content: flex-start;
    }

    .flatpickr-day {
      border-color: $c-gray2 !important;
      border-radius: 0 !important;
      border-style: solid !important;
      border-width: 0px 1px 1px 0px !important;
      box-shadow: none !important;
      margin: 0 !important;
      max-width: (100% / 7);
      width: (100% / 7);
      &:hover {
        background-color: $c-gray1;
      }

      &.flatpickr-disabled:hover {
        background-color: $c-transparent;
      }
      &.today {
        border: 1px solid $c-gray2;
        color: $c-blue-light-25;
        font-weight: 600;
      }
      &.selected,
      &.startRange,
      &.endRange {
        background-color: $c-green !important;
        color: $c-white;
        font-weight: 600;
      }
      &.startRange {
        border-right-color: $c-green !important;
      }
      &.endRange {
        border-left-color: $c-green !important;
      }
      &.inRange {
        background-color: $c-green-light-25 !important;
        border-left-color: $c-green !important;
        border-right-color: $c-green !important;
        color: $c-white;
        font-weight: 600;
      }
    }
  }
}
