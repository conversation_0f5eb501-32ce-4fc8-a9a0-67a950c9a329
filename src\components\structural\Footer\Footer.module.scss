.footer {
  display: flex;
  @include breakpoint(below, $tablet) {
    flex-direction: column;
  }
  @include breakpoint(above, $tablet) {
    flex-direction: row;
  }
}

.navigation,
.actions {
  padding: $s-base-2 $s-base-3;
}

/**
===========================
Navigation (left side)
===========================
**/

.navigation {
  align-items: center;
  background-color: $c-gray2;
  display: flex;
  flex: 1;
  justify-content: center;

  .prevBtn {
    margin-right: $s-base-1;
    padding: $s-base-1 $s-base-3;
    width: auto;
  }

  .nextBtn {
    padding: $s-base-1 $s-base-2;
    @include breakpoint(above, $tablet) {
      width: $s-base-28;
    }
  }

  button {
    margin-bottom: 0;
    white-space: nowrap;
  }
}

/**
  ===========================
  Actions (right side)
  ===========================
**/

.actions {
  align-items: center;
  background-color: $c-gray1;
  display: flex;
  justify-content: center;
  @include breakpoint(above, $tablet) {
    bottom: 0;
    position: absolute;
    right: 0;
  }

  > * {
    margin-bottom: 0;
    margin-right: $s-base-2;
    &:last-child {
      margin-right: 0;
    }
  }

  button,
  img {
    height: $s-base-5;
    width: $s-base-5;
  }
}
