/**
  ===========================
  Structure
  ===========================
**/

.wrapper {
  margin-bottom: $s-base-5;
  position: relative;
  &:last-child {
    margin-bottom: 0;
  }
}

.label {
  display: block;
  user-select: none;
  font-weight: 600;
  margin-bottom: $s-base-1;
  @include breakpoint(above, $phablet) {
    margin-right: $s-base-3;
  }
}

.field {
  border-radius: $border-radius;
  border: 1px solid $c-gray3;
  display: block;
  max-width: 100%;
  min-width: 100%;
  padding: ($s-base-1 - 1px) $s-base-6 ($s-base-1 - 1px) $s-base-3;
  resize: none;
  transition: border 0.16s;
  width: 100%;

  &:hover {
    border-color: $c-gray4;
  }

  &:focus {
    border-color: $c-green-dark-25;
    box-shadow: 0px 0px 0px 2px rgba($c-green, 0.3);
  }
}

/**
  ===========================
  States
  ===========================
**/

.disabled .field {
  background-color: $c-gray3;
  border-color: $c-gray3;
  cursor: not-allowed;
}

// .invalid .field,
// .valid .field {
//   background-color: $c-gray1;
//   background-repeat: no-repeat;
// }

.invalid .field {
  background-image: url('./errormark.svg');
  background-position: calc(100% - #{$s-base-3}) 12px;
  background-size: 18px;
  border-color: $c-danger;

  &:focus {
    box-shadow: 0px 0px 0px 2px rgba($c-danger, 0.3);
  }
}

.valid .field {
  background-image: url('./checkmark.svg');
  background-position: calc(100% - #{$s-base-3}) 9px;
  background-size: 14px;
}
