import React from 'react'
import PropTypes from 'prop-types'
import MaskedInput from 'react-text-mask'
import { get } from 'lodash'
import classNames from 'classnames'

import ErrorMessage from './ErrorMessage'

import styles from './Input.module.scss'

const Input = ({ field, form, ...props }) => {
  const fieldError = get(form.errors, field.name)
  const fieldTouched = get(form.touched, field.name) || form.submitCount > 0
  const errorAndTouched = fieldError && fieldTouched
  const validAndTouched = !fieldError && fieldTouched

  const handleChange = (e) => {
    field.onChange(e)
    props.onChange && props.onChange(e.target.value)
  }

  return (
    <div
      // prettier-ignore
      className={classNames(
        [styles.wrapper],
        { [styles.disabled]: props.disabled },
        { [styles.invalid]: errorAndTouched },
        { [styles.valid]: validAndTouched },
      )}
    >
      {props.label && (
        <label className={styles.label} htmlFor={field.name}>
          {props.label}
        </label>
      )}

      {props.mask ? (
        <MaskedInput
          {...field}
          autoComplete={props.autocomplete}
          autoFocus={props.autofocus}
          className={classNames([styles.field], [styles[props.type]])}
          disabled={props.disabled}
          id={field.name}
          inputMode={props.keyboard}
          mask={props.mask}
          onChange={handleChange}
          placeholder={props.placeholder}
          step={props.step}
          type="text"
          value={field.value || ''}
        />
      ) : (
        <input
          {...field}
          autoComplete={props.autocomplete}
          autoFocus={props.autofocus}
          className={classNames([styles.field], [styles[props.type]])}
          disabled={props.disabled}
          id={field.name}
          inputMode={props.keyboard}
          list={props.suggestions && `${field.name}_datalist`}
          max={props.max}
          maxLength={props.max}
          min={props.min}
          minLength={props.min}
          onChange={handleChange}
          placeholder={props.placeholder}
          step={props.step}
          type={props.type}
          value={field.value || ''}
        />
      )}

      {props.suggestions && (
        <datalist id={`${field.name}_datalist`}>
          {props.suggestions.map((suggestion) => (
            <option key={suggestion.value}>{suggestion.label}</option>
          ))}
        </datalist>
      )}

      <ErrorMessage render={errorAndTouched} message={fieldError} />
    </div>
  )
}

// prettier-ignore
Input.propTypes = {
  autocomplete: PropTypes.string,
  autofocus: PropTypes.bool,
  disabled: PropTypes.bool,
  keyboard: PropTypes.oneOf(['decimal', 'email', 'none', 'numeric', 'search', 'tel', 'text', 'url']),
  label: PropTypes.string,
  mask: PropTypes.any,
  max: PropTypes.number,
  min: PropTypes.number,
  onChange: PropTypes.func,
  placeholder: PropTypes.string,
  step: PropTypes.number,
  type: PropTypes.oneOf(['email', 'number', 'password', 'search', 'tel', 'text', 'url']),
  suggestions: PropTypes.arrayOf(PropTypes.shape({
    label: PropTypes.string.isRequired,
    value: PropTypes.any.isRequired,
  })),
  field: PropTypes.shape({
    name: PropTypes.string.isRequired,
    value: PropTypes.any,
    onChange: PropTypes.func.isRequired,
    onBlur: PropTypes.func.isRequired,
  }).isRequired,
  form: PropTypes.shape({
    errors: PropTypes.objectOf(PropTypes.any).isRequired,
    submitCount: PropTypes.number.isRequired,
    touched: PropTypes.objectOf(PropTypes.any).isRequired,
  }).isRequired,
}

Input.defaultProps = {
  autocomplete: 'off',
  type: 'text',
}

export default Input
