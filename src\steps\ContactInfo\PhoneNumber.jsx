import React from 'react'
import PropTypes from 'prop-types'

const PhoneNumber = ({ setFieldValue, values, form, errors }) => {
  const handlePhoneNumberChange = (event, form) => {
    let { value } = event.target

    if (value[0] === '+' && value[1] !== '1' && value.length >= 16) {
      return
    }

    let formattedNumber = value?.replace(/[\W_]/g, '')
    if (formattedNumber[0] === '1' && formattedNumber.length === 12) return

    if (value.length >= 10) formattedNumber = formatPhoneNumber(value)

    setFieldValue('contactInfo.phone', formattedNumber)

    if (
      formattedNumber[0] === '+' &&
      formattedNumber[1] !== '1' &&
      formattedNumber.length >= 16
    ) {
      form.setFieldTouched('contactInfo.phone', true, true)
      form.setFieldError(
        'contactInfo.phone',
        'Please enter a valid US phone number'
      )
      return
    }
  }

  const formatPhoneNumber = (input) => {
    // Remove all non-digit characters from the input
    const cleanedInput = input.replace(/\D/g, '')

    // Format the phone number as +1(###) ###-####
    const phoneMatchWithCountryCode = cleanedInput.match(
      /^(\d{0,1})(\d{0,3})(\d{0,3})(\d{0,4})$/
    )
    if (cleanedInput.length === 11 && phoneMatchWithCountryCode)
      return `+${phoneMatchWithCountryCode[1]}(${phoneMatchWithCountryCode[2]}) ${phoneMatchWithCountryCode[3]}-${phoneMatchWithCountryCode[4]}`

    // Format the phone number as (###) ###-####
    const phoneMatch = cleanedInput.match(/^(\d{3})(\d{0,3})(\d{0,4})$/)
    if (cleanedInput.length === 10 && phoneMatch)
      return `(${phoneMatch[1]}) ${phoneMatch[2]}-${phoneMatch[3]}`

    return input // Return the input as-is if it doesn't match the format
  }

  return (
    <div>
      <label htmlFor="contactInfo.phone" className="phoneNumberLabel">
        Phone Number
      </label>
      <input
        name="contactInfo.phone"
        id="contactInfo.phone"
        type="tel"
        autoComplete="phone"
        className="phoneNumberInput"
        onChange={(event) => handlePhoneNumberChange(event, form)}
        value={values?.contactInfo?.phone}
      />
      {errors?.contactInfo?.phone && (
        <div className="phoneNumberError" title={errors?.contactInfo?.phone}>
          {errors?.contactInfo?.phone}
        </div>
      )}
    </div>
  )
}

PhoneNumber.propTypes = {
  setFieldValue: PropTypes.func.isRequired,
  form: PropTypes.func.isRequired,
  errors: PropTypes.object.isRequired,
  values: PropTypes.object.isRequired,
}

export default PhoneNumber
