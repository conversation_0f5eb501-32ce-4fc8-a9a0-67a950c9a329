.ring {
  @include fadeIn(0.4s);
  margin: 0 auto;

  &::after,
  &::before {
    box-sizing: border-box;
    display: block;
  }

  &::after {
    animation: rotation 1s linear infinite;
    border: 4px solid $c-green;
    border-radius: 50%;
    border-left-color: $c-transparent;
    content: '';
    height: 100%;
    width: 100%;
  }
}

@keyframes rotation {
  0% {
    transform: rotate(0);
  }
  to {
    transform: rotate(359deg);
  }
}
