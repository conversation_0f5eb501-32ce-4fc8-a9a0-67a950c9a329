/**
  ===========================
  Base
  ===========================
**/

.button {
  @include t-scale-0;
  border-radius: $border-radius;
  border: 0;
  display: block;
  font-family: $t-font-openSans;
  font-weight: 700;
  margin-bottom: $s-base-1;
  padding: 4px $s-base-2;
  position: relative;
  text-align: center;
  text-decoration: none;
  transition: color 0.16s, background-color 0.16s, border-color 0.16s,
    transform 0.24s;
  user-select: none;
  width: 100%;
  @include breakpoint(above, $tablet) {
    display: inline-block;
    margin-right: $s-base-1;
    width: auto;
  }

  &:hover {
    transform: translateY(-4px);
  }

  &:last-child {
    margin-bottom: 0;
    @include breakpoint(above, $tablet) {
      margin-bottom: 0;
      margin-right: 0;
    }
  }
}

// Focus state
.button:focus:not(:active) {
  background-color: $c-orange;
}

// Icons
.button svg {
  vertical-align: text-top !important;
}

.button span:first-child {
  margin-right: $s-base-1;
}

.button span:last-child {
  margin-left: $s-base-1;
}

/**
  ===========================
  Sizes
  ===========================
**/

.button.small {
  font-weight: 400;
  padding: 4px $s-base-1;
}

.button.regular {
  padding: $s-base-1 $s-base-3;
}

.button.large {
  @include t-scale-1;
  padding: $s-base-1 $s-base-5;
}

/**
  ===========================
  Colors
  ===========================
**/

.button.default {
  background-color: $c-orange;
  border: 0;
  color: $c-white;
  &:hover {
    background-color: $c-orange-dark-25;
    border: 0;
    color: $c-white;
  }
  &:focus:not(:active) {
    background-color: $c-orange-dark-25;
  }
}

.button.confirm {
  background-color: $c-blue-dark-25;
  border: 0;
  color: $c-white;
  &:hover {
    background-color: $c-blue-dark-50;
    border: 0;
    color: $c-white;
  }
  &:focus:not(:active) {
    background-color: $c-blue-dark-50;
  }
}

.button.gray {
  background-color: $c-gray4;
  border: 0;
  color: $c-white;
  &:hover {
    background-color: $c-orange-dark-25;
    border: 0;
    color: $c-white;
  }
  &:focus:not(:active) {
    background-color: $c-orange-dark-25;
  }
}

.button.cancel {
  background-color: $c-transparent;
  border: 2px solid $c-gray3;
  color: $c-gray3;
  &:hover {
    background-color: $c-transparent;
    border-color: shade($c-gray3, 25%);
    color: shade($c-gray3, 25%);
  }
  &:focus:not(:active) {
    background-color: $c-transparent;
    border-color: shade($c-gray3, 25%);
  }
}

.button.text {
  background-color: $c-transparent;
  border: 0;
  color: $c-type-headings;
  &:hover,
  &:focus:not(:active) {
    background-color: $c-gray2;
    border: 0;
  }
}

/**
  ===========================
  Misc modifiers
  ===========================
**/

.button:disabled,
.button.disabled,
.button.loading {
  opacity: 0.64;
  pointer-events: none;
}

.button.loading {
  .text {
    color: $c-transparent;
    visibility: hidden;
  }
  .spinner {
    display: inline-block;
    opacity: 1;
  }
}

.spinner {
  color: #fff;
  display: none;
  left: 50%;
  opacity: 0;
  top: 50%;
  position: absolute;
  transform: translate(-50%, -50%);
  transition: opacity 0.16s;

  &::after,
  &::before {
    box-sizing: border-box;
    display: block;
  }

  &::after {
    animation: rotation 1s linear infinite;
    border: 3px solid $c-white;
    border-radius: 50%;
    border-left-color: $c-transparent;
    content: '';
    height: $s-base-3;
    width: $s-base-3;
  }
}

@keyframes rotation {
  0% {
    transform: rotate(0);
  }
  to {
    transform: rotate(359deg);
  }
}
