import React, { useEffect, useState } from 'react'
import { Formik, Form } from 'formik'

import Footer from '@components/structural/Footer'
import FormikEffect from './utils/FormikEffect'
import Main from '@components/structural/Main'
import Navigation from '@components/structural/Navigation'
import clearFollowingValues from '@utils/clearFollowingValues'
import config from '@src/config'
import goNextStep from './utils/goNextStep'
import scrollTop from '@utils/scrollTop'
import useBeforeUnload from '@utils/useBeforeUnload'
import Modal from '@components/ui/Modal'
import { CallUsBody, CallUsFooter } from '@components/miscellaneous/CallUs'
import ErrorFocus from './components/form/ErrorFocus'

import hotjar from './utils/hotjar'
import isCapHeaCustomer from './utils/isCapHeaCustomer'
import styles from './App.module.scss'

const App = () => {
  const [currentStepIndex, setCurrentStepIndex] = useState(0)
  const [dataWasSent, setDataWasSent] = useState(false)
  const [isIneligible, setIsIneligible] = useState(false)
  const [validationSchema, setValidationSchema] = useState(null)
  const [enableSubmitButton, setEnableSubmitButton] = useState(false)

  const steps = config.steps
  const CurrentStep = steps[currentStepIndex]

  useBeforeUnload((e) => {
    // Display confirmation dialog on page leave
    if (process.env.NODE_ENV === 'production' && !dataWasSent) {
      e.preventDefault()
      e.returnValue = ''
      return ''
    }
  })

  useEffect(() => {
    hotjar.heatMap()
  }, [])

  useEffect(() => {
    scrollTop()
  }, [currentStepIndex])

  return (
    <Formik
      enableReinitialize
      validationSchema={validationSchema}
      onSubmit={(values, formikBag) =>
        goNextStep(
          values,
          formikBag,
          CurrentStep,
          setDataWasSent,
          setCurrentStepIndex,
          setIsIneligible
        )
      }
    >
      {({ isSubmitting, values, setFieldValue, errors, isValidating }) => {
        const { path, salesOrService } = values
        // If Hvac Service Contact Info is submit step
        if (
          path &&
          path === 'hvac' &&
          salesOrService &&
          salesOrService === 'service' &&
          currentStepIndex === 2
        ) {
          CurrentStep.info.isSubmitStep = true
          setEnableSubmitButton(true)
        }

        // If CAP HEA Contact Info is submit step
        if (isCapHeaCustomer(values) && currentStepIndex === 2) {
          CurrentStep.info.isSubmitStep = true
          setEnableSubmitButton(true)
        }
        return (
          <>
            <FormikEffect onChange={clearFollowingValues} values={values} />

            <Form className={styles.app}>
              <Navigation
                currentStepIndex={currentStepIndex}
                dataWasSent={dataWasSent}
                isSubmitting={isSubmitting}
                setCurrentStepIndex={setCurrentStepIndex}
                steps={steps}
              />

              <Main>
                {isIneligible && (
                  <Modal
                    initialOpen
                    title={
                      isIneligible.when !== 'paymentAssistance'
                        ? 'Sorry'
                        : 'Give us a call'
                    }
                    body={() => <CallUsBody message={isIneligible.message} />}
                    footer={(toggleModal) => (
                      <CallUsFooter toggleModal={toggleModal} />
                    )}
                  />
                )}

                <CurrentStep
                  currentStepIndex={currentStepIndex}
                  setFieldValue={setFieldValue}
                  setValidationSchema={setValidationSchema}
                  values={values}
                  setEnableSubmitButton={setEnableSubmitButton}
                />
              </Main>

              <Footer
                currentStep={CurrentStep}
                currentStepIndex={currentStepIndex}
                isSubmitting={isSubmitting}
                setCurrentStepIndex={setCurrentStepIndex}
                stepsLength={steps.length}
                enableSubmitButton={enableSubmitButton}
                values={values}
              />
              <ErrorFocus
                isSubmitting={isSubmitting}
                isValidating={isValidating}
                errors={errors}
              />
            </Form>
          </>
        )
      }}
    </Formik>
  )
}

export default App
