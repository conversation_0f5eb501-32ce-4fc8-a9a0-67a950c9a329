import React from 'react'
import PropTypes from 'prop-types'
import moment from 'moment'

import DatePickerCalendar from '@components/ui/DatePickerCalendar'
import Loader from '@components/ui/Loader'

const PickDate = ({
  monthStart,
  handleDateChange,
  handleMonthChange,
  disabledDays,
  isLoading,
  isCap,
  isCapAgency,
  isSingleFamilyMarketRate,
  path,
}) => {
  if (isLoading) {
    return <Loader size={48} />
  }

  let daysToDisable = isSingleFamilyMarketRate ? 3 : 7
  if (isCap) {
    daysToDisable = isCapAgency ? 1 : 20
  }

  let daysOut = path === 'hea' ? 75 : 60
  if (isCap) daysOut = 90

  const disabledBefore = moment()
    .add(daysToDisable, 'day')
    .toDate()

  return (
    <>
      <p style={{ marginBottom: 0 }}>Please select a date:</p>

      <DatePickerCalendar
        onChange={handleDateChange}
        monthStart={monthStart}
        onMonthChange={handleMonthChange}
        disabledDays={disabledDays}
        disabledBefore={disabledBefore}
        disabledAfter={moment()
          .add(daysOut, 'days')
          .toDate()}
      />
    </>
  )
}

PickDate.propTypes = {
  monthStart: PropTypes.instanceOf(Date).isRequired,
  handleDateChange: PropTypes.func.isRequired,
  handleMonthChange: PropTypes.func.isRequired,
  disabledDays: PropTypes.array,
  isLoading: PropTypes.bool,
  isCap: PropTypes.bool,
  isCapAgency: PropTypes.bool,
  isSingleFamilyMarketRate: PropTypes.bool,
  path: PropTypes.string.isRequired,
}

export default PickDate
