/* eslint-disable indent */
/* eslint-disable prettier/prettier */
import React, { useEffect, useState } from 'react'
import PropTypes from 'prop-types'
import * as yup from 'yup'
import { Field } from 'formik'

import { ReactComponent as Tetra<PERSON>ogo } from '@assets/icons/tetraMainLogo.svg'

import config from '@src/config'

import Columns from '@components/ui/Columns'
import Intro from '@components/ui/Intro'
import { Address, Checkbox, Input, Select } from '@components/form'
import Modal from '@components/ui/Modal'
import {
  CallUsBody,
  CallUsFooter,
  RedirectFooter,
} from '@components/miscellaneous/CallUs'

import getURLParams from '@utils/getURLParams'
import urlPathSetter from '@utils/urlPathSetter'
import validateEmpRefCode from '@utils/validateEmpRefCode'
import isCapHeaCustomer from '@utils/isCapHeaCustomer'
import PhoneNumber from './PhoneNumber'

const ContactInfo = ({
  currentStepIndex,
  setFieldValue,
  setValidationSchema,
  values,
}) => {
  values.step = 'contactInfo'
  urlPathSetter(values)
  const urlParams = new URLSearchParams(document.location.search)
  const [addressValid, setAddressValid] = useState(false)
  const [isMaAddress, setIsMaAddress] = useState(true)
  const [isEmpRef, setIsEmpRef] = useState(false)
  const [validZip, setValidZip] = useState(true)
  const [sourceValues, setSourceValues] = useState([])

  const setSourceDropDown = () => {
    const paramsObj = getURLParams()
    const empRef = paramsObj['empRef'] === 'true'
    setIsEmpRef(empRef)
    if (empRef) {
      setSourceValues([
        {
          label: 'Employee Referral',
          value: 'Employee Referral',
        },
      ])
      return
    }
    setSourceValues(options.source)
  }

  const setInitialValuesFromParams = () => {
    const fnameParam = urlParams.get('fname')
    const lnameParam = urlParams.get('lname')

    if (fnameParam && !values.contactInfo?.firstName) {
      setFieldValue('contactInfo.firstName', fnameParam)
    }

    if (lnameParam && !values.contactInfo?.lastName) {
      setFieldValue('contactInfo.lastName', lnameParam)
    }
    setFieldValue('contactInfo.languagePref', 'English')
  }

  const options = {
    source:
      values.path === 'hvac' && values.salesOrService !== 'service'
        ? [
            { label: 'Direct Mail', value: 'Direct Mail' },
            { label: 'Email', value: 'Email' },
            { label: 'Web Search', value: 'Web Search' },
            { label: 'Social Media', value: 'Social Media' },
            { label: 'Home Energy Assessment', value: 'Home Energy Assessment' },
            { label: 'HomeAdvisor', value: 'HomeAdvisor' },
            { label: 'Leadify', value: 'Leadify' },
            { label: 'Customer Referral', value: 'Customer Referral' },
            { label: 'Employee Referral', value: 'Employee Referral' },
            { label: 'Partners', value: 'Partners' },
            { label: 'Repeat Customer', value: 'Repeat Customer' },
          ]
        : [
            { label: 'CAP', value: 'CAP' },
            { label: 'Direct Mail', value: 'Direct Mail' },
            { label: 'Web Search', value: 'Web' },
            { label: 'Social Media', value: 'Social' },
            { label: 'TV', value: 'TV' },
            { label: 'Radio', value: 'Radio' },
            { label: 'Event', value: 'Outreach Program' },
            { label: 'Partner', value: 'Partners' },
            { label: 'Employee Referral', value: 'Employee Referral' },
            { label: 'Word of Mouth', value: 'Word of mouth' },
          ],
    languagePref: [
      {
        label: 'English',
        value: 'English',
      },
      {
        label: 'Spanish',
        value: 'Spanish',
      },
      {
        label: 'Portuguese',
        value: 'Portuguese',
      },
      {
        label: 'Arabic',
        value: 'Arabic',
      },
      {
        label: 'Vietnamese',
        value: 'Vietnamese',
      },
      {
        label: 'Haitian Creole',
        value: 'Haitian Creole',
      },
      {
        label: 'Chinese(Cantonese)',
        value: 'Chinese(Cantonese)',
      },
      {
        label: 'Chinese(Mandarin)',
        value: 'Chinese(Mandarin)',
      },
      {
        label: 'Other',
        value: 'Other',
      },
    ],
  }

  useEffect(() => {
    setSourceDropDown()
    setInitialValuesFromParams()
  }, [])

  useEffect(() => {
    if (isEmpRef) {
      setFieldValue('contactInfo.source', 'Employee Referral')
    }
  }, [isEmpRef])

  useEffect(() => {
    setValidationSchema(
      yup.object().shape({
        contactInfo: yup
          .object()
          .shape({
            firstName: yup.string().required('This field is required'),
            lastName: yup.string().required('This field is required'),
            email: yup
              .string()
              .email('Please enter a valid email address')
              .required('This field is required'),
            phone: yup
              .string()
              .test(
                'validate',
                'Please enter a valid US phone number',
                async (value) => {
                  if (value === undefined) return false
                  const formattedPhone = value
                    ?.replace('+1', '')
                    ?.replace(/[\W_]/g, '')
                  return (
                    formattedPhone.length === 10 && formattedPhone[0] !== '1'
                  )
                }
              )
              .required('This field is required'),
            address: yup
              .string()
              .required('This field is required')
              .test(
                'testing',
                'Please select a valid address from the dropdown',
                async () => {
                  return addressValid
                }
              ),
            languagePref: yup
              .string()
              .oneOf(options.languagePref.map(({ value }) => value))
              .required('This field is required'),
            source: yup
              .string()
              .oneOf(options.source.map(({ value }) => value))
              .required('This field is required')
              .test('labelChange', '', function(value) {
                value === 'Employee Referral'
                  ? setIsEmpRef(true)
                  : setIsEmpRef(false)
                return true
              }),
            sourceRef: yup.string().when('source', {
              is: (source) => source === 'Employee Referral',
              then: yup
                .string()
                .required('Field is required')
                .test('validate', 'Invalid Referral Code', async (value) => {
                  if (value === undefined) return true
                  return value.length < 4 ? false : validateEmpRefCode(value)
                }),
            }),
            details: yup.string(),
            timeline: yup.string(),
            addressComponents: yup.object(),
          })
          .required(),
      })
    )
  }, [currentStepIndex, addressValid])

  return (
    <div className="wrapper-s">
      <Intro centered title="Address and Contact Information">
        {values.path === 'hea' && (
          <p>Your home meets initial Mass Save eligibility requirements!</p>
        )}
        {values.path === 'hvac' && (
          <p>Great news - you’re eligible for our HVAC services!!</p>
        )}
        <p>
          Now let&apos;s add your contact information. Your contact information
          is always kept private and only used by our installation and service
          teams.
        </p>
      </Intro>

      <Columns>
        <Field
          autocomplete="given-name"
          name="contactInfo.firstName"
          component={Input}
          label="First Name"
          type="text"
        />

        <Field
          autocomplete="family-name"
          name="contactInfo.lastName"
          component={Input}
          label="Last Name"
          type="text"
        />
      </Columns>

      <Field
        autocomplete="email"
        name="contactInfo.email"
        component={Input}
        label="Email"
        type="email"
      />

      <Columns>
        <Field
          name="contactInfo.phone"
          label="Phone Number"
          placeholder="Phone Number"
        >
          {({ form, form: { errors } }) => (
            <PhoneNumber
              setFieldValue={setFieldValue}
              form={form}
              errors={errors}
              values={values}
            />
          )}
        </Field>
        <Field
          name="contactInfo.languagePref"
          component={Select}
          label="Language Preference"
          options={options.languagePref}
        />
      </Columns>

      <Field
        name="contactInfo.textOutIn"
        component={Checkbox}
        label='I agree to be contacted by HomeWorks Energy by text, email or call, to arrange or perform energy efficiency or HVAC work. Text stop to unsubscribe. '
        type="checkbox"
        value={values?.contactInfo?.textOutIn || false}
        showPrivacyPolicy
      />

      <Field
        component={Address}
        label="Address: Start typing and choose your Massachusetts address from the drop-down menu below"
        name="contactInfo.address"
        alt="contactInfo.addressComponents"
        setAddressValid={setAddressValid}
        setIsMaAddress={setIsMaAddress}
        setValidZip={setValidZip}
      />

      <Columns>
        <Field
          name="contactInfo.source"
          component={Select}
          label="How did you hear about us?"
          options={sourceValues}
        />

        <Field
          name="contactInfo.sourceRef"
          component={Input}
          label={
            !isEmpRef
              ? 'Add referral code (optional)'
              : 'Add employee referral code'
          }
          type="text"
        />
      </Columns>
      {values.path === 'hvac' && values.salesOrService !== 'service' && (
        <Field
          name="contactInfo.details"
          component={Input}
          label="Please provide some brief details on your project"
          type="text"
        />
      )}
      {values.path === 'hvac' && values.salesOrService !== 'service' && (
        <Field
          name="contactInfo.timeline"
          component={Input}
          label="What is the timeline for your project?"
          type="text"
        />
      )}
      {!isMaAddress && (
        <Modal
          initialOpen
          title="Sorry"
          body={() => (
            <CallUsBody
              message={
                'This is not a Massachusetts address. Please choose a Massachusetts address or call a HomeWorks Energy Specialist at (781) 305-3319. Thank you!'
              }
            />
          )}
          footer={(toggleModal) => <CallUsFooter toggleModal={toggleModal} />}
        />
      )}
      {!validZip && values.path === 'hvac' && (
        <Modal
          initialOpen
          title="To Better Serve Your Neighborhood!"
          body={() => (
            <CallUsBody
              message={
                'Your home is outside the HomeWorks service territory but we can serve you through our partner, Tetra, which offers virtual visits throughout Massachusetts. Click below to schedule your free virtual visit.'
              }
              DisplayLogo={TetraLogo}
            />
          )}
          footer={(toggleModal) => (
            <RedirectFooter
              redirectUrl={config.tetraUrl}
              toggleModal={toggleModal}
              buttonTitle="Book Free Visit"
            />
          )}
        />
      )}
    </div>
  )
}

ContactInfo.info = {
  title: 'Contact Info',
  key: 'contactInfo',
}

ContactInfo.propTypes = {
  currentStepIndex: PropTypes.number.isRequired,
  setFieldValue: PropTypes.func.isRequired,
  setValidationSchema: PropTypes.func.isRequired,
  values: PropTypes.object.isRequired,
}

export default ContactInfo
