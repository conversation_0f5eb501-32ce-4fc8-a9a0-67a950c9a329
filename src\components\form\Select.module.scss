/**
  ===========================
  Structure
  ===========================
**/

.wrapper {
  margin-bottom: $s-base-5;
  position: relative;
  &:last-child {
    margin-bottom: 0;
  }
}

.label {
  display: block;
  margin-bottom: $s-base-1;
  font-weight: 600;
  user-select: none;
  @include breakpoint(above, $phablet) {
    margin-right: $s-base-3;
  }
}

.field {
  appearance: none;
  // background-color: $c-gray1;
  // background: white;
  background-image: url('./sort.svg');
  background-position: calc(100% - #{$s-base-2}) 7px;
  background-repeat: no-repeat;
  background-size: 24px;
  border-radius: $border-radius;
  border: 1px solid $c-gray3;
  display: block;
  padding: ($s-base-1 - 1px) ($s-base-8 + $s-base-1) ($s-base-1 - 1px) $s-base-3;
  position: relative;
  transition: border 0.16s;
  width: 100%;

  &::-ms-expand {
    display: none;
  }

  &:hover {
    border-color: $c-gray4;
  }

  &:focus {
    border-color: $c-green-dark-25;
    box-shadow: 0px 0px 0px 2px rgba($c-green, 0.3);
  }
}

/**
  ===========================
  States
  ===========================
**/

.disabled .field {
  background-color: $c-gray3;
  border-color: $c-gray3;
  cursor: not-allowed;
}

// .invalid .field,
// .valid .field {
//   background-color: $c-gray1;
//   background-repeat: no-repeat, no-repeat;
// }

// prettier-ignore
.invalid .field {
  background-image: url('./errormark.svg'), url('./sort.svg');
  background-position: calc(100% - #{$s-base-5}) 12px, calc(100% - #{$s-base-2}) 7px;
  background-size: 18px, 24px;
  border-color: $c-danger;

  &:focus {
    box-shadow: 0px 0px 0px 2px rgba($c-danger, 0.3);
  }
}

// prettier-ignore
.valid .field {
  background-image: url('./checkmark.svg'), url('./sort.svg');
  background-position: calc(100% - #{$s-base-5}) 9px, calc(100% - #{$s-base-2}) 7px;
  background-size: 14px, 24px;
}
