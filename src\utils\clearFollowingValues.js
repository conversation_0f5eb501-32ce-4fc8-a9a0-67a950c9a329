import config from '@src/config'

const clearFollowingValues = ({ prevValues, nextValues, formik }) => {
  // Create iterable array of value objects
  const nextValuesArr = Object.entries(nextValues)

  // Find index of the value that has changed
  const changedValueIndex = nextValuesArr.findIndex(
    (item) => prevValues[item[0]] !== nextValues[item[0]]
  )

  if (
    changedValueIndex < 0 || // Stop if no values have changed
    changedValueIndex + 1 === nextValuesArr.length // Stop if the last value was updated
  ) {
    return
  }

  // Create array of field names that come after the updated field
  const invalidFieldNames = nextValuesArr
    .filter((item, index) => index > changedValueIndex)
    .map((item) => item[0])

  invalidFieldNames.forEach((item) => {
    // Do not clear field value if its name is exempted
    if (config.clearFollowingValuesExempt?.includes(item)) return

    formik.setFieldValue(item, undefined)
  })
}

export default clearFollowingValues
