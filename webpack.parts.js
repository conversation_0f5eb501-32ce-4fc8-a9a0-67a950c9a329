const { CleanWebpackPlugin } = require('clean-webpack-plugin')
const FaviconsWebpackPlugin = require('favicons-webpack-plugin')
const HtmlWebpackPlugin = require('html-webpack-plugin')
const MiniCssExtractPlugin = require('mini-css-extract-plugin')
const OptimizeCssAssetsPlugin = require('optimize-css-assets-webpack-plugin')
const TerserWebpackPlugin = require('terser-webpack-plugin')
const webpack = require('webpack')

/*
  ===========================
  Common config parts
  ===========================
*/

exports.generateHTML = ({
  appMountId,
  appMountIds,
  lang,
  meta,
  scripts,
  title,
} = {}) => ({
  plugins: [
    new HtmlWebpackPlugin({
      appMountId,
      appMountIds,
      baseHref: '/',
      headHtmlSnippet: `<!-- Google Tag Manager -->
      <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
      new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
      j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
      'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
      })(window,document,'script','dataLayer','GTM-KFH2H2R');</script>
      <!-- End Google Tag Manager -->`,
      bodyHtmlSnippet: `<!-- Google Tag Manager (noscript) -->
       <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-KFH2H2R"
       height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
       <!-- End Google Tag Manager (noscript) -->`,
      lang,
      meta,
      mobile: true,
      template: require('html-webpack-template'),
      scripts,
      title,
    }),
  ],
})

exports.loadInlineSvg = ({ options, output, use = [] } = {}) => ({
  module: {
    rules: [
      {
        test: /\.svg$/,
        use: [
          {
            loader: '@svgr/webpack',
            options,
          },
          {
            loader: 'file-loader',
            options: { name: output },
          },
        ].concat(use),
      },
    ],
  },
})

exports.loadFiles = ({ test, output, use = [] } = {}) => ({
  module: {
    rules: [
      {
        test,
        use: [
          {
            loader: 'file-loader',
            options: { name: output },
          },
        ].concat(use),
      },
    ],
  },
})

exports.loadJavaScript = ({ test, include, exclude, use } = {}) => ({
  module: {
    rules: [
      {
        test,
        include,
        exclude,
        use,
        type: 'javascript/auto',
      },
    ],
  },
})

exports.loadCSS = ({ modules } = {}) => ({
  loader: 'css-loader',
  options: {
    sourceMap: true,
    importLoaders: 1,
    modules,
  },
})

exports.loadPostCSS = () => ({
  loader: 'postcss-loader',
  options: {
    sourceMap: true,
    plugins: () => [require('autoprefixer')({ grid: 'autoplace' })],
  },
})

exports.loadSASS = ({ importPaths, includePaths } = {}) => ({
  loader: 'sass-loader',
  options: {
    implementation: require('sass'),
    prependData: importPaths.join('').toString(),
    sourceMap: true,
    sassOptions: {
      includePaths: includePaths,
      precision: 8,
    },
  },
})

/*
  ===========================
  Production config parts
  ===========================
*/

exports.clean = (options) => ({
  plugins: [new CleanWebpackPlugin(options)],
})

exports.minifyJavaScript = () => ({
  optimization: {
    minimizer: [
      new TerserWebpackPlugin({
        extractComments: false,
        sourceMap: true,
        terserOptions: {
          output: {
            comments: false,
          },
        },
      }),
    ],
  },
})

exports.extractCSS = ({ test, include, exclude, use } = {}) => ({
  module: {
    rules: [
      {
        test,
        include,
        exclude,
        use: [MiniCssExtractPlugin.loader].concat(use),
      },
    ],
  },
  plugins: [
    new MiniCssExtractPlugin({
      filename: '[name].[hash].css',
    }),
    new OptimizeCssAssetsPlugin({
      cssProcessor: require('cssnano'),
      cssProcessorOptions: { discardComments: { removeAll: true } },
      canPrint: false,
    }),
  ],
})

exports.optimiseImages = () => ({
  loader: 'image-webpack-loader',
  options: {
    mozjpeg: { progressive: true, quality: 65 },
    optipng: { enabled: false },
    pngquant: { quality: [0.65, 0.9], speed: 4 },
    gifsicle: { interlaced: false },
  },
})

exports.generateFavicons = ({ path, title, icons } = {}) => ({
  plugins: [
    new FaviconsWebpackPlugin({
      logo: path,
      title,
      icons,
    }),
  ],
})

exports.loadMomentLocales = (locales) => ({
  plugins: [
    new webpack.ContextReplacementPlugin(/moment[/\\]locale$/, locales),
  ],
})

exports.splitVendorChunks = () => ({
  optimization: {
    splitChunks: {
      cacheGroups: {
        commons: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendor',
          chunks: 'initial',
        },
      },
    },
    runtimeChunk: {
      name: 'manifest',
    },
  },
})

/*
  ===========================
  Development config parts
  ===========================
*/

exports.generateSourceMaps = ({ type }) => ({
  devtool: type,
})

exports.devServer = ({ host, port } = {}) => ({
  devServer: {
    historyApiFallback: true,
    host,
    hot: true,
    inline: true,
    overlay: { errors: true, warnings: false },
    port,
    stats: 'errors-only',
  },
  plugins: [
    new webpack.HotModuleReplacementPlugin(),
    new webpack.NamedModulesPlugin(),
  ],
})

exports.inlineCSS = ({ test, include, exclude, use } = {}) => ({
  module: {
    rules: [
      {
        test,
        include,
        exclude,
        use: [
          {
            loader: 'style-loader',
          },
        ].concat(use),
      },
    ],
  },
})
