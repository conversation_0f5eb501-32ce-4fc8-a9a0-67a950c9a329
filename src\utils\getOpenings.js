import axios from 'axios'
import moment from 'moment'

import config from '@src/config'
import getApiURL from '@utils/getApiURL'
import getArrivalWindow from './getArrivalWindow'
import getHvacSalesArrivalWindows from './getHvacSalesArrivalWindows'

const getOpenings = async (date, monthEnd, address, program, values) => {
  try {
    let openings = []
    const zipCode = address
      .split(',')[2]
      .trim()
      .split(' ')[1]
    if (config.columbiaGasZipCodes.otherRegions.includes(zipCode))
      program = 'ColumbiaGas'
    else if (config.columbiaGasZipCodes.capeCod.includes(zipCode))
      program = 'CapeLight'

    const isoDate = moment.utc(date).format('YYYY-MM-DD')
    const {
      path,
      familySize,
      paymentAssistance,
      contactInfo: { sourceRef, languagePref },
    } = values

    const referralCode = sourceRef ? sourceRef.toLowerCase() : ''

    // Get openings from API
    if (path === 'hea') {
      const {
        results: [addressDetails],
      } = await new window.google.maps.Geocoder().geocode({
        address,
      })
      const {
        geometry: {
          location: { lat, lng },
        },
      } = addressDetails
      const isIncomeEligible = paymentAssistance === 'yes'
      const numUnit = familySize ? familySize : 1
      const res = await axios.get(
        `${getApiURL(
          'heaSlots'
        )}/openings?date=${isoDate}&address=${address}&program=${program}&isVirtualHea=no&monthEnd=${monthEnd}&numUnit=${numUnit}&latitude=${lat()}&longitude=${lng()}&referralCode=${referralCode}&languagePref=${languagePref}&isIncomeEligible=${isIncomeEligible}`
      )
      const slots = res.data.slots

      // Test whether slots exists and is iterable
      if (slots && Symbol.iterator in Object(slots)) {
        // Break up the returned value and remove empty array entries caused by the comma
        slots.forEach((slot) => {
          const splitSlot = slot
            .split(/(datetime.datetime\(\d\d\d\d,\s\d+,\s\d+,\s\d+,\s\d+\)),/g)
            .filter(Boolean)

          // Extract the actual date/time values and convert to momentjs objects so they can be worked with
          const startTimeRaw = splitSlot[0]
            .replace('datetime.datetime(', '')
            .replace(')', '')

          const endTimeRaw = splitSlot[1]
            .replace('datetime.datetime(', '')
            .replace(')', '')

          const timeParsedFormat = 'YYYY, M, D, H, m'
          const timezoneOffset = moment(startTimeRaw, timeParsedFormat)
            .toDate()
            .getTimezoneOffset()

          const startTimeParsed = moment.utc(startTimeRaw, timeParsedFormat)
          const endTimeParsed = moment.utc(endTimeRaw, timeParsedFormat)
          let appointOrArrival = `Arrival between ${getArrivalWindow(
            startTimeParsed
          )}`

          // Push onto openings array
          openings.push({
            startTimeISO: moment(startTimeParsed)
              .add(timezoneOffset, 'minutes')
              .toISOString(),
            endTimeISO: moment(endTimeParsed)
              .add(timezoneOffset, 'minutes')
              .toISOString(),
            hesID: splitSlot[2],
            region: res.data.region,
            arrivalWindow: appointOrArrival,
            timezoneOffset,
          })
        })
      }
    } else {
      const {
        results: [addressDetails],
      } = await new window.google.maps.Geocoder().geocode({
        address,
      })
      const {
        geometry: {
          location: { lat, lng },
        },
      } = addressDetails
      const validateServiceArea = async (zipCode) => {
        const url = `${getApiURL(
          'zipCodeValid'
        )}/ohcszipcodevalidation?postalcode=${zipCode}`
        const response = await axios.get(url)
        return response.data === 1
      }
      // if (values.reasonHere === 'hotWater') values.vHcs = 'yes'
      const zipCode = values.contactInfo.addressComponents.postal_code
      if (await validateServiceArea(zipCode)) {
        const res = await axios.get(
          `${getApiURL(
            'hvacOpenings'
          )}/hvacOpenings?address=${address}&date=${isoDate}&numUnit=${familySize}&nextDate=${isoDate}&latitude=${lat()}&longitude=${lng()}&languagePref=${languagePref}`
        )

        let slots = res.data.slots

        const region = res.data.region

        // Test whether slots exists and is iterable
        if (slots) {
          slots.forEach((slot) => {
            const splitSlot = slot
              .split(
                /(datetime.datetime\(\d\d\d\d,\s\d+,\s\d+,\s\d+,\s\d+\)),/g
              )
              .filter(Boolean)

            // Extract the actual date/time values and convert to momentjs objects so they can be worked with
            const startTimeRaw = splitSlot[0]
              .replace('datetime.datetime(', '')
              .replace(')', '')

            const endTimeRaw = splitSlot[1]
              .replace('datetime.datetime(', '')
              .replace(')', '')

            const timeParsedFormat = 'YYYY, M, D, H, m'
            const timezoneOffset = moment(startTimeRaw, timeParsedFormat)
              .toDate()
              .getTimezoneOffset()

            const startTimeParsed = moment.utc(startTimeRaw, timeParsedFormat)
            const endTimeParsed = moment.utc(endTimeRaw, timeParsedFormat)
            let appointOrArrival = `Arrival between ${getHvacSalesArrivalWindows(
              startTimeParsed
            )}`

            openings.push({
              startTimeISO: moment(startTimeParsed)
                .add(timezoneOffset, 'minutes')
                .toISOString(),
              endTimeISO: moment(endTimeParsed)
                .add(timezoneOffset, 'minutes')
                .toISOString(),
              hesID: splitSlot[2],
              region: region,
              arrivalWindow: appointOrArrival,
              timezoneOffset,
            })
          })
        }
      }
    }

    return openings
  } catch (error) {
    console.error(error.message)
    let errMsg =
      'Sorry, there was a problem receiving times. Please try another date, or if the problem persists, call (781) 305-3319 to schedule.'
    if (error?.response?.data?.message) errMsg = error?.response?.data?.message
    throw new Error(errMsg)
  }
}

export default getOpenings
