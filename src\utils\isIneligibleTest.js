import config from '@src/config'

const isIneligibleTest = (values) => {
  const ineligibleReason = config.ineligibleValues.find(({ when, is }) => {
    const whenIsArr = Array.isArray(when)
    const isIsFunc = typeof is === 'function'

    if ((!whenIsArr && isIsFunc) || (whenIsArr && !isIsFunc)) {
      console.error(
        "Either both 'when' and 'is' have to be strings or they have to be a string array and a function respectively"
      )
      return true
    }

    if (whenIsArr && isIsFunc) {
      return is(...when.map((entry) => values[entry]))
    }

    if (!whenIsArr && !isIsFunc) {
      return values[when] && values[when] === is
    }
  })

  if (ineligibleReason) {
    // If message is a function, call it to get the actual message
    const message =
      typeof ineligibleReason.message === 'function'
        ? ineligibleReason.message()
        : ineligibleReason.message

    return {
      ...ineligibleReason,
      message,
    }
  }

  return false
}

export default isIneligibleTest
