import React, { useEffect } from 'react'
import PropTypes from 'prop-types'
import moment from 'moment'

import Columns from '@components/ui/Columns'
import { ServiceEmergencyMessage } from '@components/miscellaneous/Warnings'
import getArrivalWindow from '@utils/getArrivalWindow'
import urlPathSetter from '@utils/urlPathSetter'
import { ReactComponent as IconSuccess } from '@assets/icons/success.svg'

import styles from './Success.module.scss'

const Success = ({ currentStepIndex, setValidationSchema, values }) => {
  values.step = 'thankYou'
  urlPathSetter(values)
  const isService =
    values.path === 'hvac' && values.salesOrService === 'service'
  useEffect(() => {
    setValidationSchema(null)
  }, [currentStepIndex])

  let appointmentTime, arriveOrAppoint, arrivalStartTime
  if (!isService) {
    let [arrivalStart] = values.schedule.split(',')
    arrivalStart = moment(arrivalStart)
    arrivalStartTime = arrivalStart
    appointmentTime = getArrivalWindow(arrivalStart)
    arriveOrAppoint = 'Arrival Window:'
  }

  let pathHtml

  const regHea = (
    <div className={styles.infoBlock}>
      <p>
        Your Home Energy Assessment will take 2-3 hours.
        <br />
        Please prepare the following for the Home Energy Specialist:
      </p>

      <ul className={styles.prepList}>
        <li>- A recent copy of your utility bills -</li>
        <li>- Clear access for basement, attic and crawl spaces -</li>
      </ul>

      <p>
        2-3 days prior to the visit, one of our customer support representatives
        will contact you to confirm your appointment via the phone number you
        provided.
      </p>
      <p>
        If we are unable to get in touch after 3 attempts, the appointment will
        need to be canceled in order to free up the time slot for other Mass
        Save Customers.
      </p>
    </div>
  )

  const regHcs = (
    <div className={styles.infoBlock}>
      <p>Your appointment will take approximately 2 hours.</p>

      <p>
        You will recieve an email confirmation shortly and can expect one of our
        customer support representatives to contact you to confirm your
        appointment and gather any additional details neccessary via the phone
        number you provided.
      </p>
      <p>
        If we are unable to get in contact with you by the day before the
        assessment, we will unfortunately need to cancel your appointment to
        allow other customers the opportunity to schedule.
      </p>
    </div>
  )

  const serviceVisitText = (
    <>
      <div>
        A representative from our service department will reach out via phone to
        ask some follow-up questions and schedule a technician to come out to
        your home at a time that is convenient for you.
      </div>
      <br />
      {values.serviceReason === 'fix' && <ServiceEmergencyMessage />}
    </>
  )

  if (values.path === 'hea') {
    pathHtml = regHea
  } else {
    pathHtml = isService ? serviceVisitText : regHcs
  }

  return (
    <div className="wrapper-xs t-left">
      <div className="t-center">
        <IconSuccess className={styles.graphic} />
      </div>

      <h2 className="t-heading-xs" style={{ marginBottom: '32px' }}>
        Thank you for choosing HomeWorks Energy!
        <span className="t-heading-xxs">
          Your partner in home energy efficiency
        </span>
      </h2>

      {!isService && (
        <>
          <p>We have reserved your assessment as follows:</p>

          <Columns classNameExt={styles.table}>
            <div>
              <strong>Date:</strong>
              <br />
              {arrivalStartTime.format('dddd MMMM Do YYYY')}
            </div>
            <div>
              <strong>{arriveOrAppoint}</strong>
              <br />
              {appointmentTime}
            </div>
          </Columns>

          <p>
            One of our{' '}
            {values.path === 'hvac'
              ? 'Account Managers '
              : 'Intake Specialists '}
            will contact you shortly to confirm your appointment, ask some
            additional questions, and gather utility information.
          </p>

          <br />
          <hr />
          <br />
        </>
      )}

      {pathHtml}
    </div>
  )
}

Success.info = {
  title: 'Success',
  key: 'success',
}

Success.propTypes = {
  currentStepIndex: PropTypes.number.isRequired,
  setValidationSchema: PropTypes.func.isRequired,
  values: PropTypes.object.isRequired,
}

export default Success
