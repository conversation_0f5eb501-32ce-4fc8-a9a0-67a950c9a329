# Online Scheduling Tool

The marketing site's embedded scheduler. Reachable on https://host.homeworksenergy.com/.

## Steps For Installation Mac/Linux v18.15.0

- Installation
To install the project, follow these steps:

Update your Node.js version to 18.15.0 or higher.
- Download the Sharp libvips-darwin-armx64.tr.br file for M1 Macbook from
- https://github.com/lovell/sharp-libvips/releases
- Set the DYLD_LIBRARY_PATH environment variable by running the following command in your terminal:
- export DYLD_LIBRARY_PATH=/usr/local/lib/libvips-8.14.2-darwin-arm64v8:$DYLD_LIBRARY_PATH

- Close and reopen your terminal to ensure the environment variable is set.
- Install Sharp by running the following command:
- npm install sharp

To verify that Sharp is installed, run the following command:
npm ls sharp



You should see the installed version of Sharp listed in the output:
│ └─┬ favicons@5.5.0
│ └── sharp@0.23.4
└── sharp@0.32.1 
Start App
 npm run dev

## Steps For Installation Mac/Linux v13.14.0

Downgrade your Node.js version to v13.14.0.

Install Specific Version for sharp compatible with nodejs 13.14.0
npm install sharp@0.23.2

You should see the installed version of Sharp listed in the output:
│ └─┬ favicons@5.5.0
│ └── sharp@0.23.4
└── sharp@0.32.1 
Start App
 npm run dev



For windowns

- make sure you have Python version no beyond  3.12
- Node v16 is ideal (Some folks have v18 and it works)