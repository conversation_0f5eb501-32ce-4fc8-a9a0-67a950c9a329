/**
  ===========================
  Reset
  ===========================
**/

*,
::before,
::after {
  background-repeat: no-repeat;
  box-sizing: border-box;
  color: inherit;
  font: inherit;
  letter-spacing: inherit;
  text-decoration: inherit;
}

* {
  -webkit-tap-highlight-color: rgba(#000000, 0);
  -webkit-text-size-adjust: 100%;
  background-color: rgba(#000000, 0);
  border: 0;
  margin: 0;
  outline: none;
  padding: 0;
  text-decoration: none;
  vertical-align: baseline;
}

::before,
::after {
  vertical-align: inherit;
}

html {
  cursor: default;
  font-size: 100%;
  tab-size: 4;
  text-size-adjust: 100%;
}

body {
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  background-color: #ffffff;
  overflow-x: hidden;
}

article,
aside,
details,
embed,
figcaption,
figure,
footer,
header,
hgroup,
main,
menu,
nav,
object,
section {
  display: block;
}

audio,
embed,
iframe,
img,
object,
video {
  height: auto;
  line-height: 0;
  max-width: 100%;
}

[hidden] {
  display: none;
}

/**
  ===========================
  Typography
  ===========================
**/

a {
  border-bottom-style: solid;
  border-bottom-width: 1px;
}

a[href^='tel:'] {
  @include breakpoint(above, $tablet) {
    border-bottom: 0;
    color: inherit;
    font-weight: inherit;
    pointer-events: none;
  }
}

b,
strong {
  font-weight: 700;
}

cite,
em,
i,
var {
  font-style: italic;
}

u {
  text-decoration: underline;
}

s,
strike,
del {
  text-decoration: line-through;
}

sup,
sub {
  font-size: 0.7em;
  line-height: 0;
  position: relative;
}

sup {
  top: -0.5em;
}

sub {
  bottom: -0.25em;
}

code,
kbd,
pre,
samp {
  font-family: monospace, monospace;
}

abbr[title] {
  border-bottom-width: 1px;
  border-style: dotted;
  cursor: help;
}

small {
  font-size: 0.8em;
}

ol,
ul {
  margin-left: 1em;
  padding-left: 1em;
}

/**
  ===========================
  Forms
  ===========================
**/

button,
[type='button'],
[type='reset'],
[type='submit'] {
  appearance: none;
  cursor: pointer;
  user-select: none;
  &::-moz-focus-inner {
    border-style: none;
    padding: 0;
  }

  * {
    pointer-events: none;
  }
}

label {
  text-align: left;
}

select {
  cursor: pointer;
}

textarea {
  overflow: auto;
}

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

::-ms-clear,
::-ms-reveal {
  display: none;
}

:-moz-ui-invalid {
  box-shadow: none;
}

/**
  ===========================
  Tables
  ===========================
**/

table {
  border-collapse: collapse;
  border-spacing: 0;
}

th {
  vertical-align: bottom;
  text-align: left;
}

td {
  cursor: auto;
}

/**
  ===========================
  Miscellaneous elements
  ===========================
**/

blockquote,
button,
dl,
form,
h1,
h2,
h3,
h4,
h5,
h6,
ol,
p,
q,
table,
ul {
  margin-bottom: 16px;
  &:last-child {
    margin-bottom: 0;
  }
}

hr {
  border: 1px solid #cccccc;
  box-sizing: content-box;
  height: 0;
  overflow: visible;
}

blockquote,
q {
  quotes: none;
  &::before,
  &::after {
    content: '';
  }

  p:not(:last-child) {
    margin-bottom: 8px;
  }

  cite::before {
    content: '- ';
  }
}

dt {
  font-weight: 700;
  &::after {
    content: ':';
  }
}

dd:not(:last-child) {
  margin-bottom: 8px;
}
