import React from 'react'
import PropTypes from 'prop-types'
import { get } from 'lodash'
import classNames from 'classnames'

import ErrorMessage from './ErrorMessage'

import styles from './Options.module.scss'

const Options = ({ field, form, ...props }) => {
  const fieldError = get(form.errors, field.name)
  const fieldTouched = get(form.touched, field.name) || form.submitCount > 0
  const errorAndTouched = fieldError && fieldTouched
  const validAndTouched = !fieldError && fieldTouched

  const handleChange = (e) => {
    field.onChange(e)
    field.onBlur(e)
    props.onChange && props.onChange(e.target.value)
  }

  const handleDoubleClick = () => {
    setTimeout(() => {
      form.handleSubmit()
    }, 150)
  }

  return (
    <fieldset
      // prettier-ignore
      className={classNames(
        [styles.wrapper],
        [styles[props.variant]],
        [styles[`col${props.maxColumns || props.options.length}`]],
        { [styles.centered]: props.centered },
        { [styles.invalid]: errorAndTouched },
        { [styles.valid]: validAndTouched },
      )}
    >
      <legend
        // prettier-ignore
        className={classNames(
          [styles.legend],
          { [styles.hidden]: !props.label },
        )}
      >
        {props.label || field.name}
      </legend>

      <div className={styles.options}>
        {props.options.map((option) => (
          <div
            key={option.value}
            // prettier-ignore
            className={classNames(
              [styles.option],
              { [styles.checked]: field.value === option.value },
              { [styles.disabled]: props.disabled || option.disabled }
            )}
          >
            <input
              {...field}
              autoComplete={props.autocomplete}
              checked={field.value === option.value}
              className={styles.field}
              disabled={props.disabled || option.disabled}
              id={`${field.name}_${option.value}`}
              onChange={handleChange}
              type="radio"
              value={option.value}
            />

            <label
              className={styles.controller}
              htmlFor={`${field.name}_${option.value}`}
              onDoubleClick={handleDoubleClick}
            >
              {option.icon && <div className={styles.icon}>{option.icon}</div>}

              <div className={styles.text}>
                {option.label && (
                  <span className={styles.label}>{option.label}</span>
                )}

                {option.description && (
                  <span className={styles.description}>
                    {option.description}
                  </span>
                )}
              </div>
            </label>
          </div>
        ))}
      </div>

      <ErrorMessage
        centered={props.centered}
        message={fieldError}
        render={errorAndTouched}
      />
    </fieldset>
  )
}

// prettier-ignore
Options.propTypes = {
  autocomplete: PropTypes.string,
  centered: PropTypes.bool,
  disabled: PropTypes.bool,
  inline: PropTypes.bool,
  label: PropTypes.string,
  maxColumns: PropTypes.oneOf([2, 3, 4, 5, 6]),
  onChange: PropTypes.func,
  variant: PropTypes.oneOf(['rectangle', 'square']),
  options: PropTypes.arrayOf(PropTypes.shape({
    description: PropTypes.string,
    disabled: PropTypes.bool,
    icon: PropTypes.node,
    label: PropTypes.string,
    value: PropTypes.any.isRequired,
  })).isRequired,
  field: PropTypes.shape({
    name: PropTypes.string.isRequired,
    value: PropTypes.any,
    onChange: PropTypes.func.isRequired,
    onBlur: PropTypes.func.isRequired,
  }).isRequired,
  form: PropTypes.shape({
    errors: PropTypes.objectOf(PropTypes.any).isRequired,
    handleSubmit: PropTypes.func.isRequired,
    submitCount: PropTypes.number.isRequired,
    touched: PropTypes.objectOf(PropTypes.any).isRequired,
  }).isRequired,
}

Options.defaultProps = {
  autocomplete: 'off',
}

export default Options
