import React from 'react'
import PropTypes from 'prop-types'

import { ReactComponent as IconPhone } from '@assets/icons/phone.svg'

const CallUsBody = ({ message, DisplayLogo }) => {
  let url = ''
  if (message.includes('https://www')) {
    const messageWithUrl = message.split(': ')
    message = messageWithUrl[0]
    url = messageWithUrl[1]
  }
  const renderMessage = (message) => {
    let parser = new DOMParser()
    parser = parser.parseFromString(message, 'text/html')
    return parser.body.innerHTML
  }
  return (
    <div className="t-center">
      <DisplayLogo style={{ height: 72, marginBottom: 24 }} />
      <p
        style={{ whiteSpace: 'pre-wrap' }}
        dangerouslySetInnerHTML={{ __html: renderMessage(message) }}
      ></p>
      {url && <a href={url}>Mass Save</a>}
    </div>
  )
}

CallUsBody.propTypes = {
  message: PropTypes.string,
  DisplayLogo: PropTypes.elementType,
}

CallUsBody.defaultProps = {
  message:
    'Sorry, it looks like your home does not qualify for a Mass Save Energy Assessment.\n\nFor more information, please call a HomeWorks Energy Specialist at (*************.',
  DisplayLogo: IconPhone,
}

export default CallUsBody
