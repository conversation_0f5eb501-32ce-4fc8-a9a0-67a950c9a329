/**
  ===========================
  Structure
  ===========================
**/

.wrapper {
  margin-bottom: $s-base-5;
  position: relative;
  &:last-child {
    margin-bottom: 0;
  }
}

.legend {
  display: block;
  margin-bottom: $s-base-1;
  font-weight: 600;
  user-select: none;
  @include breakpoint(above, $phablet) {
    margin-right: $s-base-3;
  }
}

.legend.hidden {
  left: $s-base-1;
  opacity: 0;
  position: absolute;
  top: $s-base-1;
  z-index: -1;
}

.trademark {
  font-size: 12px;
  vertical-align: top;
}

.options {
  @include breakpoint(above, $tablet) {
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    margin: (0 - $s-base-1);
  }
}

/**
  ===========================
  Option
  ===========================
**/

.spread {
  flex-grow: 1;
  max-width: 100% !important;
}

.option {
  position: relative;
  @include breakpoint(below, $tablet) {
    margin-bottom: $s-base-2;
    &:last-child {
      margin-bottom: 0;
    }
  }
  @include breakpoint(above, $tablet) {
    padding: $s-base-1 2px 2px 2px;
  }
}

@include breakpoint(above, $tablet) {
  .col1 {
    .option {
      flex-basis: 100%;
      max-width: 100%;
    }
  }

  .col2 {
    .option {
      flex-basis: 50%;
    }
  }

  .col3 {
    .option {
      flex-basis: (100% / 3);
      max-width: (100% / 3);
    }
  }

  .col4 {
    .option {
      flex-basis: 25%;
      max-width: 25%;
    }
  }

  .col5 {
    .option {
      flex-basis: 20%;
      max-width: 20%;
    }
  }

  .col6 {
    .option {
      flex-basis: (100% / 6);
      max-width: (100% / 6);
    }
  }
}

.field {
  left: $s-base-1;
  opacity: 0;
  position: absolute;
  top: $s-base-1;
  z-index: -1;
}

.controller {
  cursor: pointer;
  display: block;
  height: 100%;
  padding: $s-base-2;
  position: relative;
  transition: background-color 0.24s, box-shadow 0.24s, transform 0.24s;
  user-select: none;
}

// Circle
.controller::before,
.controller::after {
  border-radius: 50%;
  content: '';
  display: block;
  position: absolute;
}

.controller::before {
  background-color: $c-white;
  height: $s-base-3;
  left: $s-base-2;
  top: $s-base-2;
  width: $s-base-3;
  border: solid $c-gray3 2px;
}

.controller::after {
  background-color: $c-white;
  height: 12px;
  left: 22px;
  top: 22px;
  width: 12px;
}

.label {
  display: block;
  padding-left: $s-base-5;
  width: 100%;
}

.description {
  display: flex;
  padding-left: $s-base-5;
  font-size: 14px;
  opacity: 0.6;
}

/**
  ===========================
  States
  ===========================
**/

// Hover & Focus
.controller:hover,
.field:focus + .controller {
  background-color: $c-gray3;
}

.controller:hover {
  transform: translateY(-4px);
}

// Disabled
.option.disabled {
  .controller {
    background-color: $c-gray3;
    cursor: not-allowed;
    transform: translateY(0);
  }

  .controller::after {
    background-color: $c-white;
  }
}

// Checked
.option.checked {
  .controller::before {
    border: solid $cb-green 2px;
  }
  .controller::after {
    background-color: $cb-green;
  }
}

// Checked & Disabled
.option.checked.disabled {
  .controller,
  .controller::after {
    background-color: $c-gray3;
  }
}

// This is here to center the radio button circle between the text and description for radio buttons with descriptions
.withDescription::before {
  top: 28px;
}

.withDescription::after{
  top: 34px;
}
