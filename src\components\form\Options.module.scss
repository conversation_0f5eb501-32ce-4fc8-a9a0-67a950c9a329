/**
  ===========================
  Structure
  ===========================
**/

.wrapper {
  margin-bottom: $s-base-5;
  position: relative;
  &:last-child {
    margin-bottom: 0;
  }
}

.legend {
  display: block;
  margin-bottom: $s-base-1;
  font-weight: 600;
  user-select: none;
  @include breakpoint(above, $phablet) {
    margin-right: $s-base-3;
  }
}

.legend.hidden {
  left: $s-base-1;
  opacity: 0;
  position: absolute;
  top: $s-base-1;
  z-index: -1;
}

/**
  ===========================
  Option
  ===========================
**/

.option {
  position: relative;
}

.field {
  left: $s-base-1;
  opacity: 0;
  position: absolute;
  top: $s-base-1;
  z-index: -1;
}

.controller {
  background-color: $c-gray1;
  border-radius: $border-radius;
  cursor: pointer;
  display: block;
  height: 100%;
  padding: $s-base-4;
  position: relative;
  transition: background-color 0.24s, box-shadow 0.24s, transform 0.24s;
  user-select: none;
}

// Contents
.icon {
  height: $s-base-8;
}

.icon svg {
  display: inline-block;
  height: inherit;
  margin: 0;
}

.label,
.description {
  display: block;
  width: 100%;
}

.label {
  @include t-scale-2;
  color: $c-type-headings;
  font-weight: 600;
}

// Centered
.centered {
  .icon,
  .label,
  .description {
    text-align: center;
  }
}

/**
  ===========================
  Rectangle Variant
  ===========================
**/

.rectangle {
  .option {
    margin-bottom: $s-base-2;
    &:last-child {
      margin-bottom: 0;
    }
  }

  .controller {
    display: flex;
    flex-direction: column;
  }

  @include breakpoint(below, $tablet) {
    .icon {
      margin-bottom: $s-base-2;
    }
  }

  @include breakpoint(above, $tablet) {
    .controller {
      display: flex;
      flex-direction: row;
      align-items: center;
    }

    .icon {
      flex-basis: $s-base-8;
      margin-right: $s-base-4;
      max-width: $s-base-8;
      text-align: center;
    }

    .text {
      flex: 1;
    }
  }
}

/**
  ===========================
  Square Variant
  ===========================
**/

.square {
  .icon {
    margin-bottom: $s-base-2;
  }

  @include breakpoint(below, $phablet) {
    .option {
      margin-bottom: $s-base-2;
      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  @include breakpoint(above, $phablet) {
    .options {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      margin: (0 - $s-base-1);
    }

    .option {
      flex-basis: 50%;
      max-width: 50%;
      padding: $s-base-1;
    }
  }

  @include breakpoint(above, $desktop) {
    &.col2 {
      .option {
        flex-basis: 50%;
        max-width: 50%;
      }
    }

    &.col3 {
      .option {
        flex-basis: (100% / 3);
        max-width: (100% / 3);
      }
    }

    &.col4 {
      .option {
        flex-basis: 25%;
        max-width: 25%;
      }
    }

    &.col5 {
      .option {
        flex-basis: 20%;
        max-width: 20%;
      }
    }

    &.col6 {
      .option {
        flex-basis: (100% / 6);
        max-width: (100% / 6);
      }
    }
  }
}

/**
  ===========================
  States
  ===========================
**/

// Hover & Focus
.controller:hover,
.field:focus + .controller {
  background-color: $c-gray3;

  .label,
  .description {
    color: $c-white;
  }

  .icon svg,
  .icon svg * {
    fill: $c-white;
    stroke: $c-white;
  }
}

.controller:hover {
  transform: translateY(-4px);
}

// Disabled
.option.disabled {
  .controller {
    background-color: $c-gray3;
    cursor: not-allowed;
    transform: translateY(0);
  }

  .label,
  .description {
    color: $c-white;
  }

  .icon svg,
  .icon svg * {
    fill: $c-white;
    stroke: $c-white;
  }
}

// Checked
.option.checked {
  .controller {
    background-color: $c-green;
  }

  .label,
  .description {
    color: $c-white;
  }

  .icon svg,
  .icon svg * {
    fill: $c-white;
    stroke: $c-white;
  }
}
