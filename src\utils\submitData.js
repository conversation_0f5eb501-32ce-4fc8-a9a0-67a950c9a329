import axios from 'axios'
import moment from 'moment'
import config from '@src/config'
import getApiURL from '@utils/getApiURL'

const urlParams = new URLSearchParams(document.location.search)

const submitData = async (formData) => {
  const { contactInfo, schedule } = formData
  const splitSchedule = schedule ? schedule.split(',') : []
  try {
    const data = {
      // Eligibility
      utility: formData.howHeated,
      gasProvider: formData.gasProvider || '',
      account: formData.eversourceAccNo || '',
      electricProvider: formData.electricProvider,
      vHea: 'no',

      // ContactInfo
      firstname: contactInfo.firstName,
      lastname: contactInfo.lastName,
      email: contactInfo.email,
      phone: contactInfo.phone.replace('+1', '').replace(/[^\d]/g, ''), // Remove all non-digit characters
      // phoneType: contactInfo.phoneType,
      location: contactInfo.address,
      leadSource:
        urlParams.get('utm_source') === 'partners'
          ? 'Partners'
          : contactInfo.source,
      referralCode: contactInfo.sourceRef
        ? contactInfo.sourceRef.toLowerCase()
        : '',
      callFireOptOut: !contactInfo.textOutIn,

      // Schedule (only if schedule data exists)
      region: splitSchedule[3] || null,
      start: splitSchedule[0]
        ? moment.utc(splitSchedule[0]).toISOString()
        : null,
      end: splitSchedule[1]
        ? moment.utc(splitSchedule[1]).toISOString()
        : null,
      hes_id: splitSchedule[2] || null,

      // PPC Tracking
      web_form_destination: urlParams.get('webformDestination') || '',
      ppc_ad_id: urlParams.get('utm_id') || '',
      ppc_campaign: urlParams.get('utm_campaign') || '',
      ppc_content: urlParams.get('utm_content') || '',
      ppc_medium: urlParams.get('utm_medium') || '',
      ppc_source: urlParams.get('utm_source') || '',
      ppc_term: urlParams.get('utm_term') || '',

      // Home Info
      reasonHere: formData.reasonHere,
      rentOrOwn: formData.rentOrOwn,
      homeAge: formData.homeAge,
      addToExisting: formData.addToExisting,
      // familySize will always have a value except for HEA/VEA singleFamily which would make familySize 1
      familySize: formData.familySize ? formData.familySize : '1',
      howHeated: formData.howHeated,
    }

    const zipCode = contactInfo.address
      .split(',')[2]
      .trim()
      .split(' ')[1]
    if (
      formData.gasProvider &&
      config.columbiaGasZipCodes.otherRegions.includes(zipCode)
    )
      data.gasProvider = 'Columbia Gas'
    if (formData.path === 'hea') {
      data.massSave = formData.massSave
      data.isCondo = formData.isCondo
      data.ownerOrRenter = formData.ownerOrRenter ? formData.ownerOrRenter : ''
      data.singleOrMulti = formData.singleOrMulti
      data.unitNumber1 = formData.unitNumber1 ? formData.unitNumber1 : ''
      data.unitNumber2 = formData.unitNumber2 ? formData.unitNumber2 : ''
      data.unitNumber3 = formData.unitNumber3 ? formData.unitNumber3 : ''
      data.unitNumber4 = formData.unitNumber4 ? formData.unitNumber4 : ''
      data.languagePref = formData.contactInfo.languagePref
      // Only set schedule times if schedule data exists
      if (splitSchedule[0] && splitSchedule[1] && splitSchedule[4]) {
        data.startTime = moment
          .utc(splitSchedule[0])
          .subtract(splitSchedule[4], 'minutes')
          .format('HH:mm:ss')
        data.endTime = moment
          .utc(splitSchedule[1])
          .subtract(splitSchedule[4], 'minutes')
          .format('HH:mm:ss')
      } else {
        data.startTime = null
        data.endTime = null
      }
      data.isIncomeEligible = formData?.paymentAssistance === 'yes'
    }

    if (formData.path === 'hvac') {
      const {
        addressComponents: {
          street_number,
          route,
          locality,
          administrative_area_level_1,
          postal_code,
        },
        details,
        timeline,
      } = formData.contactInfo
      const formattedStreet = `${street_number} ${route}`
      const formattedCity = locality
      data.type = '000100'
      data.details = details
      data.timeline = timeline
      data.Street = formattedStreet
      data.City = formattedCity
      data.State = administrative_area_level_1
      data.PostalCode = postal_code
      data.isMultiFamily = formData.familySize && formData.familySize === '1-4'
      data.familySize = '1'
      data.languagePref = formData.contactInfo.languagePref
      data.startTime = moment
        .utc(splitSchedule[0])
        .subtract(splitSchedule[4], 'minutes')
        .format('HH:mm:ss')
      data.endTime = moment
        .utc(splitSchedule[1])
        .subtract(splitSchedule[4], 'minutes')
        .format('HH:mm:ss')
      await axios.post(
        `${getApiURL('hvacSalesSubmit')}/ohcsSubmit`,
        JSON.stringify(data)
      )
    } else {
      // else send data for hea appointments
      await axios.post(`${getApiURL('heaSubmit')}/submit`, JSON.stringify(data))
    }
  } catch (error) {
    console.error(error.message)
    throw new Error('Something went wrong and your request has not been sent.')
  }
}

export default submitData
