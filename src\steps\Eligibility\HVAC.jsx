import React, { useEffect } from 'react'
import PropTypes from 'prop-types'
import * as yup from 'yup'
import { Field } from 'formik'

import ConditionalBlock from '@components/ui/ConditionalBlock'
import Intro from '@components/ui/Intro'
import Notice from '@components/ui/Notice'
import { Radio } from '@components/form'

import HvacService from './HvacService'
import HvacSales from './HvacSales'

const Eligibility = ({ currentStepIndex, setValidationSchema, values }) => {
  const props = { currentStepIndex, setValidationSchema, values }
  const options = {
    salesOrService: [
      {
        value: 'sales',
        label: 'Purchase New Equipment',
      },
      {
        value: 'service',
        label: 'Repair/Maintain Existing Equipment',
      },
    ],
  }

  useEffect(() => {
    setValidationSchema(
      yup.object().shape({
        salesOrService: yup
          .string()
          .oneOf(options.salesOrService.map(({ value }) => value))
          .required('This field is required.'),
      })
    )
  }, [currentStepIndex, values.system])

  return (
    <div className="wrapper-xs flex-column-stretch">
      <ConditionalBlock render={values.salesOrService === 'service'}>
        <Notice type="custom1">
          {
            'If this is an emergency, please call (781) 374-7079 for immediate assistance'
          }
        </Notice>
      </ConditionalBlock>

      <Intro title="Welcome!" />
      <div>
        <Field
          component={Radio}
          maxColumns={2}
          name="salesOrService"
          options={options.salesOrService}
          label="What are you interested in?"
          spread
        />
        <ConditionalBlock render={values.salesOrService === 'service'}>
          <HvacService {...props} />
        </ConditionalBlock>
        <ConditionalBlock render={values.salesOrService === 'sales'}>
          <HvacSales {...props} />
        </ConditionalBlock>
      </div>
    </div>
  )
}

Eligibility.info = {
  title: 'Eligibility',
  key: 'eligibility',
}

Eligibility.propTypes = {
  currentStepIndex: PropTypes.number.isRequired,
  setValidationSchema: PropTypes.func.isRequired,
  values: PropTypes.object.isRequired,
}

export default Eligibility
