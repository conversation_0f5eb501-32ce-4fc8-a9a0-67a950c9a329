import React from 'react'
import PropTypes from 'prop-types'
import { get } from 'lodash'
import classNames from 'classnames'

import ErrorMessage from './ErrorMessage'

import styles from './Select.module.scss'

const Select = ({ field, form, ...props }) => {
  const fieldError = get(form.errors, field.name)
  const fieldTouched = get(form.touched, field.name) || form.submitCount > 0
  const errorAndTouched = fieldError && fieldTouched
  const validAndTouched = !fieldError && fieldTouched

  const handleChange = (e) => {
    field.onChange(e)
    field.onBlur(e)
    props.onChange && props.onChange(e)
  }

  return (
    <div
      // prettier-ignore
      className={classNames(
        [styles.wrapper],
        { [styles.disabled]: props.disabled },
        { [styles.invalid]: errorAndTouched },
        { [styles.valid]: validAndTouched },
      )}
    >
      {props.label && (
        <label className={styles.label} htmlFor={field.name}>
          {props.label}
        </label>
      )}

      <select
        {...field}
        autoComplete={props.autocomplete}
        autoFocus={props.autofocus}
        className={styles.field}
        disabled={props.disabled}
        id={field.name}
        onChange={handleChange}
        value={field.value || ''}
      >
        <option value="" />

        {props.options.map((option) => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>

      <ErrorMessage render={errorAndTouched} message={fieldError} />
    </div>
  )
}

// prettier-ignore
Select.propTypes = {
  autocomplete: PropTypes.string,
  autofocus: PropTypes.bool,
  disabled: PropTypes.bool,
  label: PropTypes.string,
  onChange: PropTypes.func,
  placeholder: PropTypes.string,
  options: PropTypes.arrayOf(PropTypes.shape({
    label: PropTypes.string.isRequired,
    value: PropTypes.any.isRequired,
  })).isRequired,
  field: PropTypes.shape({
    name: PropTypes.string.isRequired,
    value: PropTypes.any,
    onChange: PropTypes.func.isRequired,
    onBlur: PropTypes.func.isRequired,
  }).isRequired,
  form: PropTypes.shape({
    errors: PropTypes.objectOf(PropTypes.any).isRequired,
    submitCount: PropTypes.number.isRequired,
    touched: PropTypes.objectOf(PropTypes.any).isRequired,
  }).isRequired,
}

Select.defaultProps = {
  autocomplete: 'off',
}

export default Select
