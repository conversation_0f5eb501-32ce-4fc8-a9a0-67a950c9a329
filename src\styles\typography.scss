/**
  ===========================
  Headings
  ===========================
**/

@mixin t-headings {
  color: $c-type-headings;
  display: block;
  font-family: $t-font-openSans;
  margin-bottom: $s-base-1;
  width: 100%;
}

@mixin t-heading-xxs {
  @include t-headings;
  @include t-scale-2;
  color: $c-type-body;
  font-weight: 700;
}
.t-heading-xxs {
  @include t-heading-xxs;
}

@mixin t-heading-xs {
  @include t-headings;
  @include t-scale-3;
  color: $c-type-body;
  font-weight: 700;
}
.t-heading-xs {
  @include t-heading-xs;
}

@mixin t-heading-s {
  @include t-headings;
  @include t-scale-4;
  font-weight: 400;
}
.t-heading-s {
  @include t-heading-s;
}

@mixin t-heading-m {
  @include t-headings;
  @include t-scale-5;
  font-weight: 400;
}
.t-heading-m {
  @include t-heading-m;
}

@mixin t-heading-l {
  @include t-headings;
  @include t-scale-6;
  font-weight: 700;
}
.t-heading-l {
  @include t-heading-l;
}

@mixin t-heading-xl {
  @include t-headings;
  @include t-scale-7;
  font-weight: 700;
}
.t-heading-xl {
  @include t-heading-xl;
}

@mixin t-heading-xxl {
  @include t-headings;
  @include t-scale-8;
  font-weight: 700;
}
.t-heading-xxl {
  @include t-heading-xxl;
}

/**
  ===========================
  Miscellaneous
  ===========================
**/

a {
  border-bottom-style: solid;
  border-bottom-width: 1px;
  color: $c-link;
  font-weight: 600;
  &:hover,
  &:focus {
    color: $c-link-hover;
  }
}

a[href^='tel:'] {
  @include breakpoint(above, $tablet) {
    border-bottom: 0;
    color: $c-type-body;
    font-weight: 400;
    pointer-events: none;
  }
}
