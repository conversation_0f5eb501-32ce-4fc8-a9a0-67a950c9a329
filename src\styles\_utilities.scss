/**
  ===========================
  Typography
  ===========================
**/

// Alignment
.t-left {
  text-align: left;
}

.t-center {
  text-align: center;
}

.t-right {
  text-align: right;
}

.t-justify {
  text-align: justify;
}

.t-justifyCenter {
  text-align: justify;
  text-align-last: center;
}

// Transform
.t-lowercase {
  text-transform: lowercase;
}

.t-uppercase {
  text-transform: uppercase;
}

.t-capitalize {
  text-transform: capitalize;
}

// Styles
.t-bold {
  font-weight: bold;
}

.t-italic {
  font-style: italic;
}

.t-small {
  @include t-scale-0;
}

.t-muted {
  color: $c-type-muted;
}

// Wrapping
.t-nowrap {
  white-space: nowrap;
}

.t-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.t-clip {
  overflow: hidden;
  text-overflow: clip;
  white-space: nowrap;
}

.t-break {
  hyphens: auto;
  word-break: break-word;
  word-wrap: break-word;
}

/**
  ===========================
  Wrappers
  ===========================
**/

.wrapper-xs,
.wrapper-s,
.wrapper-m,
.wrapper-l {
  margin-left: auto;
  margin-right: auto;
  padding-left: $s-base-2;
  padding-right: $s-base-2;
}

.wrapper-xs {
  max-width: $phablet;
}

.wrapper-s {
  max-width: $tablet;
}

.wrapper-m {
  max-width: $laptop;
}

.wrapper-l {
  max-width: $desktop;
}

.flex-column {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.flex-colum-stretch {
  display: flex;
  flex-direction: column;
  align-items: stretch;
}

/**
  ===========================
  Animations
  ===========================
**/

.fadeIn {
  @include fadeIn(0.6s);
}

/**
==============================
Infos
==============================
**/
.vHeaInfo {
  font-size: 14px;
  color: #99c543;
}

.phoneNumberInput {
  border-radius: 24px;
  border: 1px solid #8d8b7e;
  display: block;
  padding: 7px 48px 7px 24px;
  transition: border 0.16s;
  width: 100%;
}

.phoneNumberLabel {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.phoneNumberError {
  bottom: -24px;
  max-height: 24px;
  margin: 0 24px;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #dc3545;
  font-style: italic;
  font-size: 13px;
  line-height: 1.8461538462;
}
