.notice {
  @include shakeHorizontal;
  @include t-scale-0;
  border-radius: $border-radius;
  font-weight: 600;
  margin-bottom: $s-base-2;
  padding: $s-base-2 $s-base-4;
  text-align: center;
  &:last-child {
    margin-bottom: 0;
  }
}

.error {
  background-color: $c-danger;
  color: $c-white;
}

.info {
  background-color: $c-info;
  color: $c-white;
}

.success {
  background-color: $c-success;
  color: $c-white;
}

.warning {
  background-color: $c-warning;
  color: $c-white;
}

.custom1 {
  background-color: $c-danger;
  color: $c-white;
}
