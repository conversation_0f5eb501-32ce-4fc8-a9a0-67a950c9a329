import React from 'react'
import PropTypes from 'prop-types'
import classNames from 'classnames'

import styles from './Notice.module.scss'

const Notice = ({ children, type }) => (
  <div className={classNames([styles.notice], [styles[type]])}>{children}</div>
)

Notice.propTypes = {
  children: PropTypes.node.isRequired,
  type: PropTypes.oneOf(['error', 'info', 'success', 'warning', 'custom1'])
    .isRequired,
}

export default Notice
