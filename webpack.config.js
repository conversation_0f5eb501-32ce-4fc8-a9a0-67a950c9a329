const merge = require('webpack-merge')
const parts = require('./webpack.parts')
const path = require('path')

const PATHS = {
  src: path.join(__dirname, 'src', 'index.js'),
  public: path.join(__dirname, 'public'),
  favicon: path.join(__dirname, 'src', 'assets', 'favicon.png'),
}

const SITEINFO = {
  title: 'Online Scheduler | HomeWorks Energy',
  lang: 'en',
  scripts: [
    'https://maps.googleapis.com/maps/api/js?key=AIzaSyB42MkuopfybH0MAiwwqaIrPjlN1jpsM3w&libraries=places&language=en',
  ],
  meta: { robots: 'noindex' },
}

const globalStyleImports = {
  importPaths: [
    "@import 'src/styles/base.scss';",
    "@import 'src/styles/variables.scss';",
    "@import 'src/styles/mixins.scss';",
    "@import 'src/styles/typography.scss';",
  ],
  includePaths: [
    path.resolve(__dirname, 'src', 'styles', 'base.scss'),
    path.resolve(__dirname, 'src', 'styles', 'variables.scss'),
    path.resolve(__dirname, 'src', 'styles', 'mixins.scss'),
    path.resolve(__dirname, 'src', 'styles', 'typography.scss'),
  ],
}

/*
  ===========================
  Common config
  ===========================
*/

const commonConfig = merge([
  {
    entry: PATHS.src,
    output: {
      path: PATHS.public,
      filename: '[name].[hash].js',
    },
    resolve: {
      alias: {
        '@assets': path.resolve(__dirname, 'src', 'assets'),
        '@components': path.resolve(__dirname, 'src', 'components'),
        '@pages': path.resolve(__dirname, 'src', 'pages'),
        '@src': path.resolve(__dirname, 'src'),
        '@store': path.resolve(__dirname, 'src', 'store'),
        '@utils': path.resolve(__dirname, 'src', 'utils'),
      },
      extensions: ['.js', '.jsx', '.ts', '.tsx', '.json'],
    },
  },
  parts.generateHTML({
    title: SITEINFO.title,
    lang: SITEINFO.language,
    appMountIds: ['modal', 'app'],
    meta: SITEINFO.meta,
    scripts: SITEINFO.scripts,
  }),
  parts.loadInlineSvg({
    output: './assets/[name].[hash].[ext]',
  }),
  parts.loadFiles({
    test: /\.(mp4|webm|ogg)$/,
    output: './assets/video/[name].[hash].[ext]',
  }),
  parts.loadFiles({
    test: /\.(eot|ttf|woff|woff2)(\?v=\d+\.\d+\.\d+)?$/,
    output: './assets/fonts/[name].[hash].[ext]',
  }),
  parts.loadFiles({
    test: /\.html$/,
    output: './[name].[ext]',
  }),
  parts.loadJavaScript({
    test: /\.(ts|tsx|js|jsx)$/,
    exclude: /[\\/]node_modules[\\/]/,
    use: ['babel-loader', 'eslint-loader'],
  }),
])

/*
  ===========================
  Production config
  ===========================
*/

const productionConfig = merge([
  parts.clean(),
  parts.minifyJavaScript(),
  parts.extractCSS({
    test: /\.(scss|css)$/,
    exclude: /\.module\.(scss|css)$/,
    use: [
      parts.loadCSS(),
      parts.loadPostCSS(),
      parts.loadSASS(globalStyleImports),
    ],
  }),
  parts.extractCSS({
    test: /\.module\.(scss|css)$/,
    use: [
      parts.loadCSS({ modules: true }),
      parts.loadPostCSS(),
      parts.loadSASS(globalStyleImports),
    ],
  }),
  parts.loadFiles({
    test: /\.(jpe?g|png|gif)$/,
    output: './assets/images/[name].[hash].[ext]',
    use: [parts.optimiseImages()],
  }),
  parts.generateFavicons({
    path: PATHS.favicon,
    title: SITEINFO.title,
    icons: {
      android: false,
      appleIcon: false,
      appleStartup: false,
      firefox: false,
    },
  }),
  parts.loadMomentLocales(/en-us/),
  parts.splitVendorChunks(),
])

/*
  ===========================
  Development config
  ===========================
*/

const developmentConfig = merge([
  parts.generateSourceMaps({
    type: 'cheap-module-eval-source-map',
  }),
  parts.devServer({
    host: process.env.HOST || '0.0.0.0',
    port: process.env.PORT || 8000,
  }),
  parts.inlineCSS({
    test: /\.(scss|css)$/,
    exclude: /\.module\.(scss|css)$/,
    use: [
      parts.loadCSS(),
      parts.loadPostCSS(),
      parts.loadSASS(globalStyleImports),
    ],
  }),
  parts.inlineCSS({
    test: /\.module\.(scss|css)$/,
    use: [
      parts.loadCSS({ modules: true }),
      parts.loadPostCSS(),
      parts.loadSASS(globalStyleImports),
    ],
  }),
  parts.loadFiles({
    test: /\.(jpe?g|png|gif)$/,
    output: './assets/images/[name].[hash].[ext]',
  }),
])

/*
  ===========================
  Export
  ===========================
*/

module.exports = (mode) => {
  if (mode === 'production') {
    return merge(commonConfig, productionConfig, { mode })
  }

  return merge(commonConfig, developmentConfig, { mode })
}
