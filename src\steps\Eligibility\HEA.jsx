/* eslint-disable indent */
import React, { useEffect } from 'react'
import PropTypes from 'prop-types'
import * as yup from 'yup'
import { Field } from 'formik'

import ConditionalBlock from '@components/ui/ConditionalBlock'
import Intro from '@components/ui/Intro'
import { Radio, Select, Input } from '@components/form'

const Eligibility = ({ currentStepIndex, setValidationSchema, values }) => {
  const options = {
    boolean: [
      { value: 'yes', label: 'Yes' },
      { value: 'no', label: 'No' },
    ],
    howHeated: [
      {
        value: 'Gas',
        label: 'Gas',
        description: "You don't need to refuel your heating system.",
      },
      {
        value: 'Oil',
        label: 'Oil',
        description: 'A truck comes and delivers to your home.',
      },
      {
        value: 'Propane',
        label: 'Propane',
        description: 'A small tank is next to your heating system.',
      },
      {
        value: 'Electric',
        label: 'Electricity',
        description: "Equipment is connected to your home's electric system.",
      },
    ],
    gasProvider: [
      { label: 'Eversource', value: 'Eversource' },
      { label: 'National Grid', value: 'National Grid' },
      { label: 'Columbia Gas', value: 'Columbia Gas' },
    ],
    electricProvider: [
      { label: 'National Grid', value: 'NationalGrid' },
      { label: 'Eversource', value: 'Eversource' },
      { label: 'Municipal', value: 'Municipal' },
    ],
    gasProviderWithBerkshire: [
      { label: 'National Grid', value: 'National Grid' },
      { label: 'Eversource', value: 'Eversource' },
      { label: 'Columbia Gas', value: 'Columbia Gas' },
      { label: 'Berkshire Gas', value: 'Berkshire Gas' },
    ],
    electricProviderWithBerkshire: [
      { label: 'National Grid', value: 'NationalGrid' },
      { label: 'Eversource', value: 'Eversource' },
      { label: 'Municipal', value: 'Municipal' },
      { label: 'Berkshire Gas', value: 'Berkshire Gas' },
    ],
    familySize: [
      { label: '1', value: '1' },
      { label: '2', value: '2' },
      { label: '3', value: '3' },
      { label: '4', value: '4' },
      { label: '5+', value: '5+' },
    ],
    ownerOrRenter: [
      { label: 'Renter', value: 'Tenant' },
      { label: 'Owner', value: 'Owner-Occupied' },
    ],
    singleOrMulti: [
      { label: 'Single Family', value: 'Single family' },
      { label: '2-Unit Multi-Family', value: '2-Unit Multi-Family' },
      { label: '3-Unit Multi-Family', value: '3-Unit Multi-Family' },
      { label: '4-Unit Multi-Family', value: '4-Unit Multi-Family' },
      { label: '5+ Multi-Family', value: '5+ Multi-Family' },
    ],
  }

  useEffect(() => {
    setValidationSchema(
      yup.object().shape({
        twoYears: yup
          .string()
          .oneOf(options.boolean.map(({ value }) => value))
          .required('This field is required'),
        paymentAssistance: yup
          .string()
          .oneOf(options.boolean.map(({ value }) => value))
          .when('twoYears', {
            is: 'no',
            then: yup.string().required('This field is required'),
          }),
        singleOrMulti: yup
          .string()
          .oneOf(options.singleOrMulti.map(({ value }) => value))
          .when(['twoYears', 'paymentAssistance'], {
            is: 'no',
            then: yup.string().required('This field is required'),
          }),
        ownerOrRenter: yup
          .string()
          .oneOf(options.ownerOrRenter.map(({ value }) => value))
          .when(['singleOrMulti'], {
            is: (singleOrMulti) =>
              singleOrMulti &&
              !['Single family', '5+ Multi-Family'].includes(singleOrMulti),
            then: yup.string().required('This field is required'),
          }),
        isCondo: yup
          .string()
          .oneOf(options.boolean.map(({ value }) => value))
          .when(['ownerOrRenter', 'singleOrMulti'], {
            is: (ownerOrRenter, singleOrMulti) =>
              (singleOrMulti && ['Single family'].includes(singleOrMulti)) ||
              ['Tenant', 'Owner-Occupied'].includes(ownerOrRenter),
            then: yup.string().required('This field is required'),
          }),
        familySize: yup
          .string()
          .oneOf(options.familySize.map(({ value }) => value))
          .when(['singleOrMulti', 'ownerOrRenter', 'isCondo'], {
            is: (singleOrMulti, isCondo) =>
              isCondo &&
              singleOrMulti &&
              singleOrMulti &&
              [
                '2-Unit Multi-Family',
                '3-Unit Multi-Family',
                '4-Unit Multi-Family',
              ].includes(singleOrMulti),
            then: yup.string().required('This field is required'),
          }),
        unitNumber1: yup
          .string()
          .when(['singleOrMulti', 'familySize', 'isCondo'], {
            is: (singleOrMulti, familySize, isCondo) =>
              (singleOrMulti &&
                [
                  '2-Unit Multi-Family',
                  '3-Unit Multi-Family',
                  '4-Unit Multi-Family',
                ].includes(singleOrMulti) &&
                familySize &&
                Number(familySize) >= 1) ||
              (singleOrMulti &&
                singleOrMulti === 'Single family' &&
                isCondo === 'yes'),
            then: yup
              .string()
              .trim()
              .required('This field is required')
              .max(10, 'Max 10 Characters'),
          }),
        unitNumber2: yup.string().when(['singleOrMulti', 'familySize'], {
          is: (singleOrMulti, familySize) =>
            singleOrMulti &&
            [
              '2-Unit Multi-Family',
              '3-Unit Multi-Family',
              '4-Unit Multi-Family',
            ].includes(singleOrMulti) &&
            familySize &&
            Number(familySize) >= 2,
          then: yup
            .string()
            .trim()
            .required('This field is required')
            .max(10, 'Max 10 Characters'),
        }),
        unitNumber3: yup.string().when(['singleOrMulti', 'familySize'], {
          is: (singleOrMulti, familySize) =>
            singleOrMulti &&
            [
              '2-Unit Multi-Family',
              '3-Unit Multi-Family',
              '4-Unit Multi-Family',
            ].includes(singleOrMulti) &&
            familySize &&
            Number(familySize) >= 3,
          then: yup
            .string()
            .trim()
            .required('This field is required')
            .max(10, 'Max 10 Characters'),
        }),
        unitNumber4: yup.string().when(['singleOrMulti', 'familySize'], {
          is: (singleOrMulti, familySize) =>
            singleOrMulti &&
            [
              '2-Unit Multi-Family',
              '3-Unit Multi-Family',
              '4-Unit Multi-Family',
            ].includes(singleOrMulti) &&
            familySize &&
            Number(familySize) === 4,
          then: yup
            .string()
            .trim()
            .required('This field is required')
            .max(10, 'Max 10 Characters'),
        }),
        howHeated: yup
          .string()
          .oneOf(options.howHeated.map(({ value }) => value))
          .when(['isCondo'], {
            is: (isCondo) => ['yes', 'no'].includes(isCondo),
            then: yup.string().required('This field is required'),
          }),
        gasProvider: yup.string().when('howHeated', {
          is: 'Gas',
          then: yup
            .string()
            .oneOf(options.gasProviderWithBerkshire.map(({ value }) => value))
            .required('This field is required'),
        }),
        electricProvider: yup
          .string()
          .oneOf(
            options.electricProviderWithBerkshire.map(({ value }) => value)
          )
          .when(['howHeated', 'gasProvider'], {
            is: (howHeated, gasProvider) =>
              (howHeated && howHeated !== 'Gas') ||
              (howHeated === 'Gas' && gasProvider),
            then: yup.string().required('This field is required'),
          }),
      })
    )
  }, [currentStepIndex, values.system])

  const unitNumberInputs = (currentValues) => {
    const { singleOrMulti, familySize } = currentValues
    const numberOfUnitNumbersToCollect =
      singleOrMulti === 'Single family' ? 1 : Number(familySize)
    const unitNumberInputs = []
    for (let k = 0; k < numberOfUnitNumbersToCollect; k++) {
      unitNumberInputs.push(
        <Field
          name={`unitNumber${k + 1}`}
          component={Input}
          label={
            k === 0
              ? 'Please enter each unit number you would like to schedule'
              : ''
          }
          type="text"
          max={10}
        />
      )
    }
    return unitNumberInputs
  }

  const renderUnitNumers = (currentValues) => {
    const { singleOrMulti, familySize, isCondo } = currentValues
    if (singleOrMulti) {
      if (singleOrMulti === 'Single family' && isCondo === 'yes') return true
      if (
        [
          '2-Unit Multi-Family',
          '3-Unit Multi-Family',
          '4-Unit Multi-Family',
        ].includes(singleOrMulti)
      ) {
        return ['1', '2', '3', '4'].includes(familySize)
      }
    }
    return false
  }

  const renderHowHeated = (currentValues) => {
    const { singleOrMulti, familySize, unitNumber1, isCondo } = currentValues
    if (singleOrMulti && singleOrMulti === 'Single family') {
      return isCondo === 'yes' ? unitNumber1 && unitNumber1.length : true
    }
    if (['1', '2', '3', '4'].includes(familySize)) {
      return (
        currentValues[`unitNumber${Number(familySize)}`] &&
        currentValues[`unitNumber${Number(familySize)}`].length
      )
    }
    return false
  }

  const filteredUnitsSchedulingToday = (currentValues) => {
    const { singleOrMulti } = currentValues
    if (singleOrMulti && singleOrMulti !== 'Single Family') {
      return options.familySize.filter((option) => {
        const { value } = option
        return parseFloat(value) <= parseFloat(singleOrMulti)
      })
    }
  }

  return (
    <div className="wrapper-xs flex-column">
      <Intro title="Determine Your Home's Eligibility" />

      <div>
        <Field
          component={Radio}
          label="Have you received a Mass Save home energy assessment within the last two years?"
          maxColumns={2}
          name="twoYears"
          options={options.boolean}
        />

        <ConditionalBlock render={values.twoYears === 'no'}>
          <Field
            component={Radio}
            label="Are you on a utility discount rate? (R2 or R4 on your utility bill)"
            maxColumns={2}
            name="paymentAssistance"
            options={options.boolean}
          />
        </ConditionalBlock>

        <ConditionalBlock render={values.paymentAssistance}>
          <Field
            name="singleOrMulti"
            component={Select}
            label="Is your home a single-family residence, or does it have multiple units?"
            options={options.singleOrMulti}
          />
        </ConditionalBlock>

        <ConditionalBlock
          render={
            values.singleOrMulti &&
            !['Single family', '5+ Multi-Family'].includes(values.singleOrMulti)
          }
        >
          <Field
            name="ownerOrRenter"
            component={Radio}
            maxColumns={2}
            label="Are you the tenant or landlord?"
            options={options.ownerOrRenter}
          />
        </ConditionalBlock>

        <ConditionalBlock
          render={
            values.ownerOrRenter ||
            (values.singleOrMulti &&
              ['Single family'].includes(values.singleOrMulti))
          }
        >
          <Field
            name="isCondo"
            component={Radio}
            maxColumns={2}
            label="Are you part of a condo association?"
            options={options.boolean}
          />
        </ConditionalBlock>

        <ConditionalBlock
          render={
            values.isCondo &&
            values.singleOrMulti &&
            [
              '2-Unit Multi-Family',
              '3-Unit Multi-Family',
              '4-Unit Multi-Family',
            ].includes(values.singleOrMulti)
          }
        >
          <Field
            name="familySize"
            component={Radio}
            maxColumns={2}
            label="How many units are we scheduling today?"
            options={filteredUnitsSchedulingToday(values)}
          />
        </ConditionalBlock>

        <ConditionalBlock render={values.isCondo && renderUnitNumers(values)}>
          <div> {unitNumberInputs(values)} </div>
        </ConditionalBlock>

        <ConditionalBlock render={values.isCondo && renderHowHeated(values)}>
          <Field
            name="howHeated"
            component={Radio}
            maxColumns={2}
            label="How is your home heated?"
            options={options.howHeated}
            withDescription={true}
          />
        </ConditionalBlock>

        <ConditionalBlock render={values.howHeated === 'Gas'}>
          <Field
            name="gasProvider"
            component={Radio}
            maxColumns={2}
            label="Who is your gas provider?"
            options={
              values.paymentAssistance && values.paymentAssistance === 'yes'
                ? options.gasProviderWithBerkshire
                : options.gasProvider
            }
          />
        </ConditionalBlock>

        <ConditionalBlock
          render={
            (values.howHeated && values.howHeated !== 'Gas') ||
            (values.howHeated === 'Gas' && values.gasProvider)
          }
        >
          <Field
            name="electricProvider"
            component={Radio}
            maxColumns={2}
            label="Who is your electric provider?"
            options={
              values.paymentAssistance && values.paymentAssistance === 'yes'
                ? options.electricProviderWithBerkshire
                : options.electricProvider
            }
          />
        </ConditionalBlock>
      </div>
    </div>
  )
}

Eligibility.info = {
  title: 'Eligibility',
  key: 'eligibility',
}

Eligibility.propTypes = {
  currentStepIndex: PropTypes.number.isRequired,
  setValidationSchema: PropTypes.func.isRequired,
  values: PropTypes.object.isRequired,
}

export default Eligibility
