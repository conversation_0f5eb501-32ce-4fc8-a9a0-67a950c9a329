import React from 'react'
import PropTypes from 'prop-types'
import HEA from './HEA'
import HVAC from './HVAC'

import urlPathSetter from '@utils/urlPathSetter'

const Eligibility = ({ currentStepIndex, setValidationSchema, values }) => {
  values.step = 'eligibility'
  urlPathSetter(values)
  const props = { currentStepIndex, setValidationSchema, values }
  if (values.path === 'hea') return <HEA {...props} />
  if (values.path === 'hvac') return <HVAC {...props} />
  return null
}

Eligibility.info = {
  title: 'Eligibility',
  key: 'eligibility',
}

Eligibility.propTypes = {
  currentStepIndex: PropTypes.number.isRequired,
  setValidationSchema: PropTypes.func.isRequired,
  values: PropTypes.object.isRequired,
}

export default Eligibility
