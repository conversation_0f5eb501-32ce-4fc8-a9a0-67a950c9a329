import React, { useEffect, useState } from 'react'
import PropTypes from 'prop-types'
import classNames from 'classnames'
import { createPortal } from 'react-dom'

import styles from './Modal.module.scss'

const Modal = ({ body, footer, initialOpen, size, title, trigger }) => {
  const [open, setOpen] = useState(initialOpen || false)

  const toggleModal = () => {
    setOpen((prevOpen) => !prevOpen)
  }

  const handleBgClick = (e) => {
    if (open && e.target === e.currentTarget) {
      setOpen(false)
    }
  }

  const handleKeyDown = (e) => {
    if (open && e.key === 'Escape') {
      setOpen(false)
    }
  }

  useEffect(() => {
    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [open])

  return (
    <>
      {trigger && trigger(toggleModal)}

      {open &&
        createPortal(
          <div
            className={styles.modal}
            onClick={handleBgClick}
            role="button"
            tabIndex={-1}
          >
            <div className={classNames([styles.dialog], [styles[size]])}>
              <div className={styles.content}>
                <div className={styles.header}>
                  <h2 className={styles.title}>{title}</h2>

                  <button
                    type="button"
                    className={styles.close}
                    onClick={() => toggleModal()}
                  >
                    ×
                  </button>
                </div>

                <div className={styles.body}>{body(toggleModal)}</div>

                {footer && (
                  <div className={styles.footer}>{footer(toggleModal)}</div>
                )}
              </div>
            </div>
          </div>,
          document.getElementById('modal')
        )}
    </>
  )
}

Modal.propTypes = {
  body: PropTypes.func.isRequired,
  footer: PropTypes.func,
  initialOpen: PropTypes.bool,
  size: PropTypes.oneOf(['small', 'medium', 'large']),
  title: PropTypes.string,
  trigger: PropTypes.func,
}

Modal.defaultProps = {
  size: 'medium',
}

export default Modal
