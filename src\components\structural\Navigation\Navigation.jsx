import React from 'react'
import PropTypes from 'prop-types'
import classNames from 'classnames'

import goToStep from '@utils/goToStep'

import { ReactComponent as IconLogo } from '@assets/logo.svg'

import styles from './Navigation.module.scss'

const Navigation = ({
  currentStepIndex,
  dataWasSent,
  isSubmitting,
  setCurrentStepIndex,
  steps,
}) => (
  <div className={styles.navigation}>
    <a
      href="https://www.homeworksenergy.com"
      target="_self"
      className={styles.logo}
    >
      <IconLogo />
    </a>

    <nav className={styles.wrapper}>
      {steps.map((step, index) => (
        <button
          disabled={index >= currentStepIndex || isSubmitting || dataWasSent}
          key={step.info.key}
          onClick={() => goToStep(index, setCurrentStepIndex)}
          type="button"
          // prettier-ignore
          className={classNames(
            [styles.item],
            { [styles.active]: index === currentStepIndex },
            { [styles.disabled]: index >= currentStepIndex || isSubmitting || dataWasSent },
            { [styles.completed]: (index !== currentStepIndex && currentStepIndex > index) || dataWasSent },
            { [styles.last]: index === steps.length - 1 }
          )}
        >
          {step.info.title}
        </button>
      ))}
    </nav>
  </div>
)

Navigation.propTypes = {
  currentStepIndex: PropTypes.number.isRequired,
  dataWasSent: PropTypes.bool,
  isSubmitting: PropTypes.bool,
  setCurrentStepIndex: PropTypes.func.isRequired,
  steps: PropTypes.arrayOf(PropTypes.func).isRequired,
}

export default Navigation
