import isIneligibleTest from '@utils/isIneligibleTest'
import sleep from '@utils/sleep'
import submitData from '@utils/submitData'
import submitServiceData from '@utils/submitServiceData'
import submitCapHeaLead from '@utils/submitCapHeaLead'
import isCapHeaCustomer from '@utils/isCapHeaCustomer'

const goNextStep = async (
  values,
  { setFormikState, setSubmitting },
  currentStep,
  setDataWasSent,
  setCurrentStepIndex,
  setIsIneligible
) => {
  try {
    document.activeElement.blur()
    setIsIneligible(false)
    const isIneligible = isIneligibleTest(values)
    // User is eligible
    if (!isIneligible) {
      // Check if this step should be a submit step (either static or dynamic)
      const isSubmitStep = currentStep.info.isSubmitStep ||
        (currentStep.info.getIsSubmitStep && currentStep.info.getIsSubmitStep(values))

      if (isSubmitStep) {
        if (process.env.NODE_ENV === 'production') {
          if (isCapHeaCustomer(values)) {
            await submitCapHeaLead(values)
          } else if (values.path === 'hvac' && values.salesOrService === 'service') {
            await submitServiceData(values)
          } else {
            await submitData(values)
          }
        } else {
          if (isCapHeaCustomer(values)) {
            await submitCapHeaLead(values)
          } else if (values.path === 'hvac' && values.salesOrService === 'service') {
            await submitServiceData(values)
          } else if (values.path === 'hvac') {
            await submitData(values)
          } else {
            await sleep()
            console.log(values)
            await submitData(values)
          }
        }

        setDataWasSent(true)
      }

      // Skip Schedule step for HVAC Service and CAP HEA customers
      if (
        isSubmitStep &&
        ((values.path === 'hvac' && values.salesOrService === 'service') ||
         isCapHeaCustomer(values))
      ) {
        setCurrentStepIndex((prevState) => prevState + 2)
      } else {
        setCurrentStepIndex((prevState) => prevState + 1)
      }
    }

    // User is ineligible
    if (isIneligible) {
      setIsIneligible(isIneligible)
    }

    setFormikState((prevState) => ({ ...prevState, submitCount: 0 }))
  } catch (error) {
    alert(error.message)
  }

  setSubmitting(false)
}

export default goNextStep
