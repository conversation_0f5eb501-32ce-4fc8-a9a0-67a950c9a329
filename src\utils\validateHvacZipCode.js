import axios from 'axios'
import getApiURL from '@utils/getApiURL'

const validateHvacZipCode = async (zipCode) => {
  try {
    const url = `${getApiURL(
      'zipCodeValid'
    )}/ohcszipcodevalidation?postalcode=${zipCode}`
    const response = await axios.get(url)
    return response.data === 1
  } catch (error) {
    console.error(error.message)
    throw new Error(error.message)
  }
}

export default validateHvacZipCode
