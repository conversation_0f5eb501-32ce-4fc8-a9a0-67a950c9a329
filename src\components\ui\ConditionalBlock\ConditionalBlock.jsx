import React, { useRef } from 'react'
import PropTypes from 'prop-types'
import classNames from 'classnames'

import styles from './ConditionalBlock.module.scss'

const ConditionalBlock = ({
  children,
  fadeIn,
  noMargin,
  render,
  scrollIntoView,
}) => {
  const conditionalBlockEl = useRef(null)

  if (scrollIntoView && conditionalBlockEl.current) {
    conditionalBlockEl.current.scrollIntoView({ behavior: 'smooth' })
  }

  if (render) {
    return (
      <div
        ref={conditionalBlockEl}
        // prettier-ignore
        className={classNames(
          [styles.conditionalBlock],
          { [styles.noMargin]: noMargin },
          { [styles.fadeIn]: fadeIn }
        )}
      >
        {children}
      </div>
    )
  }

  return null
}

ConditionalBlock.propTypes = {
  children: PropTypes.node.isRequired,
  fadeIn: PropTypes.bool,
  noMargin: PropTypes.bool,
  scrollIntoView: PropTypes.bool,
  render: PropTypes.oneOfType([
    PropTypes.bool,
    PropTypes.func,
    PropTypes.number,
    PropTypes.string,
  ]),
}

ConditionalBlock.defaultProps = {
  fadeIn: true,
  scrollIntoView: true,
}

export default ConditionalBlock
