import axios from 'axios'
import getApiURL from '@utils/getApiURL'

const urlParams = new URLSearchParams(document.location.search)

const submitServiceData = async (formData) => {
  const { contactInfo } = formData
  const data = {
    // PPC Tracking
    ppc_ad_id__c: urlParams.get('utm_id') || '',
    ppc_campaign__c: urlParams.get('utm_campaign') || '',
    ppc_content__c: urlParams.get('utm_content') || '',
    ppc_medium__c: urlParams.get('utm_medium') || '',
    ppc_source__c: urlParams.get('utm_source') || '',
    ppc_term__c: urlParams.get('utm_term') || '',
    leadSource:
      urlParams.get('utm_source') === 'partners'
        ? 'Partners'
        : contactInfo.source,
  }
  const info = {
    formData,
    data,
  }

  try {
    await axios.post(
      `${getApiURL('hvacServiceSubmit')}/hvacServiceSubmit`,
      JSON.stringify(info)
    )
  } catch (error) {
    console.error(error.message)
    throw new Error('Something went wrong and your request has not been sent.')
  }
}

export default submitServiceData
