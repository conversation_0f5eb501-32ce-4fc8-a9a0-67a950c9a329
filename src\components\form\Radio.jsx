import React from 'react'
import PropTypes from 'prop-types'
import { get } from 'lodash'
import classNames from 'classnames'

import ErrorMessage from './ErrorMessage'

import styles from './Radio.module.scss'

const Radio = ({ field, form, ...props }) => {
  const fieldError = get(form.errors, field.name)
  const fieldTouched = get(form.touched, field.name) || form.submitCount > 0
  const errorAndTouched = fieldError && fieldTouched
  const validAndTouched = !fieldError && fieldTouched

  const handleChange = (e) => {
    field.onChange(e)
    field.onBlur(e)
    props.onChange && props.onChange(e.target.value)
  }
  return (
    <fieldset
      // prettier-ignore
      className={classNames(
        [styles.wrapper],
        [styles[`col${props.maxColumns}`]],
        { [styles.invalid]: errorAndTouched },
        { [styles.valid]: validAndTouched },
      )}
    >
      <legend
        // prettier-ignore
        className={classNames(
          [styles.legend],
          { [styles.hidden]: !props.label },
        )}
      >
        {props.label || field.name}
      </legend>

      <div className={styles.options}>
        {props.options.map((option) => (
          <div
            key={option.value}
            // prettier-ignore
            className={classNames(
              [styles.option],
              { [styles.spread]: props.spread },
              { [styles.checked]: field.value === option.value },
              { [styles.disabled]: props.disabled || option.disabled }
            )}
          >
            <label
              className={classNames([styles.controller], {
                [styles.withDescription]: props.withDescription,
              })}
              htmlFor={`${field.name}_${option.value}`}
            >
              <input
                {...field}
                autoComplete={props.autocomplete}
                checked={field.value === option.value}
                className={styles.field}
                disabled={props.disabled || option.disabled}
                id={`${field.name}_${option.value}`}
                onChange={handleChange}
                type="radio"
                value={option.value}
              />
              <span className={styles.label}>{option.label}</span>
              {option.description && (
                <span className={styles.description}>{option.description}</span>
              )}
            </label>
          </div>
        ))}
      </div>

      <ErrorMessage render={errorAndTouched} message={fieldError} />
    </fieldset>
  )
}

// prettier-ignore
Radio.propTypes = {
  autocomplete: PropTypes.string,
  disabled: PropTypes.bool,
  spread: PropTypes.bool,
  label: PropTypes.string,
  maxColumns: PropTypes.oneOf([1, 2, 3, 4, 5, 6]),
  onChange: PropTypes.func,
  options: PropTypes.arrayOf(PropTypes.shape({
    disabled: PropTypes.bool,
    label: PropTypes.any.isRequired,
    value: PropTypes.any.isRequired,
  })).isRequired,
  field: PropTypes.shape({
    name: PropTypes.string.isRequired,
    value: PropTypes.any,
    onChange: PropTypes.func.isRequired,
    onBlur: PropTypes.func.isRequired,
  }).isRequired,
  form: PropTypes.shape({
    errors: PropTypes.objectOf(PropTypes.any).isRequired,
    submitCount: PropTypes.number.isRequired,
    touched: PropTypes.objectOf(PropTypes.any).isRequired,
  }).isRequired,
  withDescription: PropTypes.bool,
}

Radio.defaultProps = {
  autocomplete: 'off',
  maxColumns: 3,
  withDescription: false,
}

export default Radio
