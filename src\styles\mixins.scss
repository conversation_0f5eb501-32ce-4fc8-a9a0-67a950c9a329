/**
  ===========================
  Animations
  ===========================
**/

@mixin slideInTop($duration: 0.4s, $distance: $s-base-1) {
  animation: slideInTop $duration cubic-bezier(0.25, 0.46, 0.45, 0.94) both;

  @keyframes slideInTop {
    0% {
      opacity: 0;
      transform: translateY((0 - $distance));
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }
}

@mixin fadeIn($duration: 1.2s) {
  animation: fadeIn $duration cubic-bezier(0.39, 0.575, 0.565, 1) both;

  @keyframes fadeIn {
    0% {
      opacity: 0;
    }
    100% {
      opacity: 1;
    }
  }
}

@mixin shakeHorizontal() {
  animation: shakeHorizontal 0.8s cubic-bezier(0.455, 0.03, 0.515, 0.955) both;

  @keyframes shakeHorizontal {
    0%,
    100% {
      transform: translateX(0);
    }
    10%,
    30%,
    50%,
    70% {
      transform: translateX(-10px);
    }
    20%,
    40%,
    60% {
      transform: translateX(10px);
    }
    80% {
      transform: translateX(8px);
    }
    90% {
      transform: translateX(-8px);
    }
  }
}
